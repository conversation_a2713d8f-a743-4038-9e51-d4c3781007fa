#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام البوت الموحد مع الإشعارات التلقائية
بوت واحد يتعامل مع الأوامر والإشعارات معاً
"""

import telebot
import pyodbc
import time
import threading
from telebot import types
from datetime import datetime, timedelta
import logging
import json
import sys

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('unified_bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# بيانات الاتصال بقواعد البيانات
# قاعدة البيانات الأساسية للفواتير
PRIMARY_SERVER = '41.41.48.194'
PRIMARY_DATABASE = 'ams'
PRIMARY_USERNAME = 'sa2'
PRIMARY_PASSWORD = '@a123admin4'

# قاعدة البيانات الاحتياطية للمزامنة
BACKUP_SERVER = '41.41.48.97'
BACKUP_DATABASE = 'ams'
BACKUP_USERNAME = 'sa'
BACKUP_PASSWORD = '@a123admin4'

# قاعدة بيانات التتبع على الخادم الاحتياطي
TRACKING_SERVER = '41.41.48.97'
TRACKING_DATABASE = 'ams_tracking'
TRACKING_USERNAME = 'sa'
TRACKING_PASSWORD = '@a123admin4'

# توكن البوت
BOT_TOKEN = "8240250213:AAFd1kY6J-Ng-IOiEpEQnx9O1RnTAhMUxE0"

# المستخدمين المصرح لهم والمشتركين في الإشعارات (القيم الافتراضية)
AUTHORIZED_USERS = [1107000748]  # القيم الافتراضية

NOTIFICATION_SUBSCRIBERS = [1107007448]  # القيم الافتراضية

class UnifiedBotSystem:
    def __init__(self):
        self.bot = telebot.TeleBot(BOT_TOKEN)
        self.is_running = False
        self.notification_thread = None
        self.last_check = datetime.now() - timedelta(minutes=2)

        # جلب المستخدمين المصرح لهم من قاعدة البيانات
        self.load_authorized_users()
        self.check_interval = 30  # فحص كل 30 ثانية

        # إعداد نظام التتبع الذكي
        self.tracking_connection = None
        self.bot_connection = None
        self.setup_tracking_tables()

        # متغيرات لتتبع آخر الفواتير والمصروفات باستخدام التاريخ
        # بدء من الآن لتجنب إرسال البيانات القديمة
        self.last_invoice_check = datetime.now()
        self.last_expense_check = datetime.now()
        self.last_box_check = datetime.now()

        # تحميل آخر تواريخ من قاعدة التتبع
        self.load_last_ids()

        # إعداد معالجات البوت
        self.setup_bot_handlers()
        
    def get_primary_connection(self):
        """الاتصال بقاعدة البيانات الأساسية"""
        return pyodbc.connect(
            f'DRIVER={{ODBC Driver 17 for SQL Server}};'
            f'SERVER={PRIMARY_SERVER};DATABASE={PRIMARY_DATABASE};UID={PRIMARY_USERNAME};PWD={PRIMARY_PASSWORD}',
            timeout=30
        )

    def get_backup_connection(self):
        """الاتصال بقاعدة البيانات الاحتياطية"""
        return pyodbc.connect(
            f'DRIVER={{ODBC Driver 17 for SQL Server}};'
            f'SERVER={BACKUP_SERVER};DATABASE={BACKUP_DATABASE};UID={BACKUP_USERNAME};PWD={BACKUP_PASSWORD}',
            timeout=30
        )

    def load_authorized_users(self):
        """جلب المستخدمين المصرح لهم من قاعدة البيانات"""
        global AUTHORIZED_USERS, NOTIFICATION_SUBSCRIBERS
        try:
            # الاتصال بقاعدة بيانات البوت
            conn_str = (
                "DRIVER={ODBC Driver 17 for SQL Server};"
                "SERVER=terraa.ddns.net,4100;"
                "DATABASE=codeza-etsh;"
                "UID=sa;"
                "PWD=@a123admin4;"
                "TrustServerCertificate=yes;"
            )

            conn = pyodbc.connect(conn_str, timeout=30)
            cursor = conn.cursor()

            # جلب المستخدمين المصرح لهم
            cursor.execute("SELECT user_id FROM authorized_users WHERE is_active = 1")
            authorized_rows = cursor.fetchall()
            AUTHORIZED_USERS = [row[0] for row in authorized_rows]

            # جلب المشتركين في الإشعارات
            cursor.execute("SELECT user_id FROM notification_subscribers WHERE is_active = 1")
            subscriber_rows = cursor.fetchall()
            NOTIFICATION_SUBSCRIBERS = [row[0] for row in subscriber_rows]

            cursor.close()
            conn.close()

            # إذا لم توجد بيانات، استخدم القيم الافتراضية الصحيحة
            # تثبيت المستخدمين الأساسيين المصرح لهم باستخدام البوت
            AUTHORIZED_USERS = [1107007448, 7412659917]

            # تثبيت المستخدمين الأساسيين المشتركين في استلام الإشعارات
            NOTIFICATION_SUBSCRIBERS = [1107007448, 7412659917]

            logging.info(f"تم جلب {len(AUTHORIZED_USERS)} مستخدم مصرح و {len(NOTIFICATION_SUBSCRIBERS)} مشترك")

        except Exception as e:
            logging.warning(f"خطأ في جلب المستخدمين من قاعدة البيانات: {e}")
            # استخدام القيم الافتراضية
            AUTHORIZED_USERS = [1107000748, 7841269917]
            NOTIFICATION_SUBSCRIBERS = [1107000748, 7841269917]

    def get_bot_connection(self):
        """الاتصال بقاعدة بيانات البوت - معطل"""
        # قاعدة بيانات البوت غير متاحة حالياً
        raise Exception("قاعدة بيانات البوت غير متاحة")

    def is_authorized(self, user_id):
        """التحقق من صلاحية المستخدم"""
        return user_id in AUTHORIZED_USERS

    def get_notification_subscribers(self):
        """الحصول على المشتركين في الإشعارات"""
        return NOTIFICATION_SUBSCRIBERS

    def setup_bot_handlers(self):
        """إعداد معالجات البوت"""
        
        @self.bot.message_handler(commands=['start'])
        def start_command(message):
            """أمر البدء"""
            user_id = message.from_user.id
            username = message.from_user.username or "غير محدد"
            first_name = message.from_user.first_name or "مستخدم"
            
            if not self.is_authorized(user_id):
                welcome_msg = f"مرحباً {first_name}!\n\n"
                welcome_msg += "هذا بوت إدارة الفواتير المتقدم\n"
                welcome_msg += "للحصول على الصلاحية، استخدم الأمر /id"
                self.bot.send_message(message.chat.id, welcome_msg)
                return
            
            welcome_msg = f"مرحباً {first_name}! 👋\n\n"
            welcome_msg += "🤖 بوت إدارة الفواتير الموحد\n"
            welcome_msg += "📊 تقارير مفصلة + إشعارات تلقائية\n"
            welcome_msg += "⚡ اختر ما تريد من الأزرار أدناه:"

            # إنشاء لوحة مفاتيح ثابتة
            markup = telebot.types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True, one_time_keyboard=False)

            # الصف الأول
            btn_invoices = telebot.types.KeyboardButton("📄 آخر الفواتير")
            btn_expenses = telebot.types.KeyboardButton("💰 آخر المصروفات")
            markup.add(btn_invoices, btn_expenses)

            # الصف الثاني
            btn_status = telebot.types.KeyboardButton("📊 حالة النظام")
            btn_today = telebot.types.KeyboardButton("📅 تقرير اليوم")
            markup.add(btn_status, btn_today)

            # الصف الثالث
            btn_search = telebot.types.KeyboardButton("🔍 بحث متقدم")
            btn_help = telebot.types.KeyboardButton("❓ المساعدة")
            markup.add(btn_search, btn_help)

            self.bot.send_message(message.chat.id, welcome_msg, reply_markup=markup)

        @self.bot.message_handler(commands=['id'])
        def id_command(message):
            """أمر طلب الصلاحية"""
            user_id = message.from_user.id
            username = message.from_user.username or "غير محدد"
            first_name = message.from_user.first_name or "مستخدم"
            
            if self.is_authorized(user_id):
                self.bot.send_message(message.chat.id, "✅ أنت مصرح لك بالفعل!")
                return
            
            # إشعار المديرين
            admin_msg = f"🔔 طلب صلاحية جديد:\n\n"
            admin_msg += f"👤 الاسم: {first_name}\n"
            admin_msg += f"🆔 المعرف: {user_id}\n"
            admin_msg += f"📱 اليوزر: @{username}\n"
            admin_msg += f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            
            # إرسال للمديرين
            admin_ids = [1107007448, 7412659917]
            for admin_id in admin_ids:
                try:
                    markup = types.InlineKeyboardMarkup()
                    markup.add(
                        types.InlineKeyboardButton("✅ موافقة", callback_data=f"approve_{user_id}"),
                        types.InlineKeyboardButton("❌ رفض", callback_data=f"reject_{user_id}")
                    )
                    self.bot.send_message(admin_id, admin_msg, reply_markup=markup)
                except:
                    pass
            
            self.bot.send_message(message.chat.id, "✅ تم إرسال طلب الصلاحية للمدير، انتظر الموافقة")

        # معالج الرسائل النصية للأزرار الثابتة
        @self.bot.message_handler(func=lambda message: True)
        def handle_text_messages(message):
            """معالج الرسائل النصية للأزرار الثابتة"""
            if not self.is_authorized(message.from_user.id):
                self.bot.reply_to(message, "❌ غير مصرح لك باستخدام هذا البوت")
                return

            text = message.text

            if text == "📄 آخر الفواتير":
                self.get_daily_summary(message.chat.id)
            elif text == "💰 آخر المصروفات":
                self.get_expenses_report(message.chat.id, 7)
            elif text == "📊 حالة النظام":
                self.get_system_status(message.chat.id)
            elif text == "📅 تقرير اليوم":
                self.get_daily_summary(message.chat.id)
            elif text == "🔍 بحث متقدم":
                self.bot.send_message(message.chat.id, "🔍 البحث المتقدم\n\nاختر نوع البحث:", reply_markup=self.get_main_keyboard())
            elif text == "❓ المساعدة":
                help_msg = """
❓ مساعدة البوت

📋 الأزرار المتاحة:
• 📄 آخر الفواتير - عرض ملخص الفواتير اليومية
• 💰 آخر المصروفات - عرض المصروفات الأخيرة
• 📊 حالة النظام - معلومات حالة النظام
• 📅 تقرير اليوم - تقرير شامل لليوم
• 🔍 بحث متقدم - خيارات بحث متقدمة
• ❓ المساعدة - هذه الرسالة

🔔 الإشعارات التلقائية:
سيتم إرسال إشعارات فورية عند إضافة فواتير أو مصروفات جديدة

✅ البوت يعمل 24/7
                """
                self.bot.send_message(message.chat.id, help_msg)
            else:
                self.bot.send_message(message.chat.id, "❓ لم أفهم طلبك، استخدم الأزرار أدناه")

        @self.bot.callback_query_handler(func=lambda call: call.data.startswith(('approve_', 'reject_')))
        def handle_approval(call):
            """معالجة الموافقة أو الرفض"""
            try:
                action, user_id = call.data.split('_', 1)
                user_id = int(user_id)
                
                if action == 'approve':
                    self.bot.answer_callback_query(call.id, "✅ تم قبول الطلب")
                    self.bot.edit_message_text("✅ تم قبول الطلب", call.message.chat.id, call.message.message_id)
                    
                    try:
                        self.bot.send_message(user_id, "🎉 تم قبول طلب الصلاحية! يمكنك الآن استخدام البوت\nاستخدم /start للبدء")
                    except:
                        pass
                        
                else:  # reject
                    self.bot.answer_callback_query(call.id, "❌ تم رفض الطلب")
                    self.bot.edit_message_text("❌ تم رفض الطلب", call.message.chat.id, call.message.message_id)
                    
                    try:
                        self.bot.send_message(user_id, "❌ تم رفض طلب الصلاحية")
                    except:
                        pass
                        
            except Exception as e:
                logging.error(f"خطأ في معالجة الموافقة: {e}")
                self.bot.answer_callback_query(call.id, f"❌ خطأ: {e}")

        # الأوامر المتقدمة الجديدة
        @self.bot.message_handler(commands=['companies'])
        def companies_command(message):
            """أمر عرض الشركات والفروع"""
            if not self.is_authorized(message.from_user.id):
                self.bot.reply_to(message, "❌ غير مصرح لك باستخدام هذا البوت")
                return
            self.get_companies_report(message.chat.id)

        @self.bot.message_handler(commands=['expenses'])
        def expenses_command(message):
            """أمر تقرير المصروفات التفصيلي"""
            if not self.is_authorized(message.from_user.id):
                self.bot.reply_to(message, "❌ غير مصرح لك باستخدام هذا البوت")
                return
            self.get_detailed_expenses_report(message.chat.id)

        @self.bot.message_handler(commands=['cash'])
        def cash_command(message):
            """أمر حركات الصندوق والبنوك"""
            if not self.is_authorized(message.from_user.id):
                self.bot.reply_to(message, "❌ غير مصرح لك باستخدام هذا البوت")
                return
            self.get_cash_movements_report(message.chat.id)

        @self.bot.message_handler(commands=['performance'])
        def performance_command(message):
            """أمر تقرير الأداء الشامل"""
            if not self.is_authorized(message.from_user.id):
                self.bot.reply_to(message, "❌ غير مصرح لك باستخدام هذا البوت")
                return
            self.get_performance_report(message.chat.id)

        @self.bot.message_handler(commands=['invoices'])
        def invoices_command(message):
            """أمر البحث في الفواتير"""
            if not self.is_authorized(message.from_user.id):
                self.bot.reply_to(message, "❌ غير مصرح لك باستخدام هذا البوت")
                return
            self.get_invoices_search(message.chat.id)

        @self.bot.message_handler(commands=['stats'])
        def stats_command(message):
            """أمر الإحصائيات السريعة"""
            if not self.is_authorized(message.from_user.id):
                self.bot.reply_to(message, "❌ غير مصرح لك باستخدام هذا البوت")
                return
            self.get_quick_stats(message.chat.id)

        @self.bot.callback_query_handler(func=lambda call: call.data == "detailed_invoices")
        def detailed_invoices_callback(call):
            """معالج الفواتير المفصلة"""
            user_id = call.from_user.id

            if not self.is_authorized(user_id):
                self.bot.answer_callback_query(call.id, "❌ غير مصرح لك")
                return

            self.bot.answer_callback_query(call.id, "📋 فواتير مفصلة...")

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("📅 عدد الأيام", callback_data="invoices_days"),
                types.InlineKeyboardButton("📆 تاريخ محدد", callback_data="invoices_date")
            )
            markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="main_menu"))

            self.bot.send_message(call.message.chat.id, "📋 فواتير مفصلة\n\nاختر نوع البحث:", reply_markup=markup)

        @self.bot.callback_query_handler(func=lambda call: call.data == "invoices_days")
        def invoices_days_callback(call):
            """فواتير بعدد الأيام"""
            self.bot.answer_callback_query(call.id, "📅 عدد الأيام...")

            msg = self.bot.send_message(call.message.chat.id,
                              "📋 فواتير مفصلة\n\n📅 كم يوم تريد؟ (اكتب رقم من 1 إلى 365)\n\nمثال: 1 لليوم الحالي، 7 لآخر أسبوع",
                              reply_markup=self.get_cancel_keyboard())
            self.bot.register_next_step_handler(msg, self.process_days_for_detailed_invoices)

        @self.bot.callback_query_handler(func=lambda call: call.data == "invoices_date")
        def invoices_date_callback(call):
            """فواتير بتاريخ محدد"""
            self.bot.answer_callback_query(call.id, "📆 تاريخ محدد...")

            msg = self.bot.send_message(call.message.chat.id,
                              "📋 فواتير مفصلة\n\n📆 اكتب التاريخ بالصيغة: DD-MM-YYYY\n\nمثال: 04-09-2025",
                              reply_markup=self.get_cancel_keyboard())
            self.bot.register_next_step_handler(msg, self.process_date_for_detailed_invoices)

        @self.bot.callback_query_handler(func=lambda call: call.data == "daily_summary")
        def daily_summary_callback(call):
            """ملخص اليوم"""
            user_id = call.from_user.id
            
            if not self.is_authorized(user_id):
                self.bot.answer_callback_query(call.id, "❌ غير مصرح لك")
                return
            
            self.bot.answer_callback_query(call.id, "📊 جاري إعداد ملخص اليوم...")
            self.send_daily_summary(call.message.chat.id)

        @self.bot.callback_query_handler(func=lambda call: call.data == "debt_reports")
        def debt_reports_callback(call):
            """تقارير المديونيات"""
            user_id = call.from_user.id
            
            if not self.is_authorized(user_id):
                self.bot.answer_callback_query(call.id, "❌ غير مصرح لك")
                return
            
            self.bot.answer_callback_query(call.id, "💰 تقارير المديونيات...")
            
            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("📋 فواتير غير مدفوعة", callback_data="unpaid_invoices"),
                types.InlineKeyboardButton("🚚 فواتير غير مسلمة", callback_data="undelivered_invoices")
            )
            markup.add(types.InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="main_menu"))
            
            self.bot.send_message(call.message.chat.id, "💰 اختر نوع تقرير المديونيات:", reply_markup=markup)

        @self.bot.callback_query_handler(func=lambda call: call.data == "unpaid_invoices")
        def unpaid_invoices_callback(call):
            """الفواتير غير المدفوعة"""
            user_id = call.from_user.id
            
            if not self.is_authorized(user_id):
                self.bot.answer_callback_query(call.id, "❌ غير مصرح لك")
                return
            
            self.bot.answer_callback_query(call.id, "🔍 جاري البحث عن الفواتير غير المدفوعة...")
            self.send_unpaid_invoices_report(call.message.chat.id)

        @self.bot.callback_query_handler(func=lambda call: call.data == "expenses_reports")
        def expenses_reports_callback(call):
            """تقارير المصروفات"""
            user_id = call.from_user.id

            if not self.is_authorized(user_id):
                self.bot.answer_callback_query(call.id, "❌ غير مصرح لك")
                return

            self.bot.answer_callback_query(call.id, "💸 تقارير المصروفات...")

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("📅 عدد الأيام", callback_data="expenses_days"),
                types.InlineKeyboardButton("📆 تاريخ محدد", callback_data="expenses_date")
            )
            markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="main_menu"))

            self.bot.send_message(call.message.chat.id, "💸 تقارير المصروفات\n\nاختر نوع البحث:", reply_markup=markup)

        @self.bot.callback_query_handler(func=lambda call: call.data == "expenses_days")
        def expenses_days_callback(call):
            """مصروفات بعدد الأيام"""
            self.bot.answer_callback_query(call.id, "📅 عدد الأيام...")

            msg = self.bot.send_message(call.message.chat.id,
                              "💸 تقارير المصروفات\n\n📅 كم يوم تريد؟ (اكتب رقم من 1 إلى 365)\n\nمثال: 30 لآخر شهر",
                              reply_markup=self.get_cancel_keyboard())
            self.bot.register_next_step_handler(msg, self.process_days_for_expenses)

        @self.bot.callback_query_handler(func=lambda call: call.data == "expenses_date")
        def expenses_date_callback(call):
            """مصروفات بتاريخ محدد"""
            self.bot.answer_callback_query(call.id, "📆 تاريخ محدد...")

            msg = self.bot.send_message(call.message.chat.id,
                              "💸 تقارير المصروفات\n\n📆 اكتب التاريخ بالصيغة: DD-MM-YYYY\n\nمثال: 04-09-2025",
                              reply_markup=self.get_cancel_keyboard())
            self.bot.register_next_step_handler(msg, self.process_date_for_expenses)

        @self.bot.callback_query_handler(func=lambda call: call.data == "box_movements")
        def box_movements_callback(call):
            """حركات الصندوق"""
            user_id = call.from_user.id

            if not self.is_authorized(user_id):
                self.bot.answer_callback_query(call.id, "❌ غير مصرح لك")
                return

            self.bot.answer_callback_query(call.id, "💰 حركات الصندوق...")

            msg = self.bot.send_message(call.message.chat.id,
                              "💰 حركات الصندوق\n\n📅 كم يوم تريد؟ (اكتب رقم من 1 إلى 365)\n\nمثال: 7 لآخر أسبوع",
                              reply_markup=self.get_cancel_keyboard())
            self.bot.register_next_step_handler(msg, self.process_days_for_box_movements)

        @self.bot.callback_query_handler(func=lambda call: call.data == "companies_performance")
        def companies_performance_callback(call):
            """أداء الشركات"""
            user_id = call.from_user.id

            if not self.is_authorized(user_id):
                self.bot.answer_callback_query(call.id, "❌ غير مصرح لك")
                return

            self.bot.answer_callback_query(call.id, "🏢 أداء الشركات...")

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("📅 عدد الأيام", callback_data="companies_days"),
                types.InlineKeyboardButton("📆 تاريخ محدد", callback_data="companies_date")
            )
            markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="main_menu"))

            self.bot.send_message(call.message.chat.id, "🏢 أداء الشركات\n\nاختر نوع البحث:", reply_markup=markup)

        @self.bot.callback_query_handler(func=lambda call: call.data == "companies_days")
        def companies_days_callback(call):
            """أداء الشركات بعدد الأيام"""
            self.bot.answer_callback_query(call.id, "📅 عدد الأيام...")

            msg = self.bot.send_message(call.message.chat.id,
                              "🏢 أداء الشركات\n\n📅 كم يوم تريد؟ (اكتب رقم من 1 إلى 365)\n\nمثال: 30 لآخر شهر",
                              reply_markup=self.get_cancel_keyboard())
            self.bot.register_next_step_handler(msg, self.process_days_for_companies_performance)

        @self.bot.callback_query_handler(func=lambda call: call.data == "companies_date")
        def companies_date_callback(call):
            """أداء الشركات بتاريخ محدد"""
            self.bot.answer_callback_query(call.id, "📆 تاريخ محدد...")

            msg = self.bot.send_message(call.message.chat.id,
                              "🏢 أداء الشركات\n\n📆 اكتب التاريخ بالصيغة: DD-MM-YYYY\n\nمثال: 04-09-2025",
                              reply_markup=self.get_cancel_keyboard())
            self.bot.register_next_step_handler(msg, self.process_date_for_companies_performance)

        @self.bot.callback_query_handler(func=lambda call: call.data == "box_movements")
        def box_movements_callback(call):
            """حركات الصندوق"""
            user_id = call.from_user.id

            if not self.is_authorized(user_id):
                self.bot.answer_callback_query(call.id, "❌ غير مصرح لك")
                return

            self.bot.answer_callback_query(call.id, "💰 حركات الصندوق...")

            markup = types.InlineKeyboardMarkup()
            markup.add(
                types.InlineKeyboardButton("📅 عدد الأيام", callback_data="box_days"),
                types.InlineKeyboardButton("📆 تاريخ محدد", callback_data="box_date")
            )
            markup.add(types.InlineKeyboardButton("🔙 العودة", callback_data="main_menu"))

            self.bot.send_message(call.message.chat.id, "💰 حركات الصندوق\n\nاختر نوع البحث:", reply_markup=markup)

        @self.bot.callback_query_handler(func=lambda call: call.data == "box_days")
        def box_days_callback(call):
            """حركات الصندوق بعدد الأيام"""
            self.bot.answer_callback_query(call.id, "📅 عدد الأيام...")

            msg = self.bot.send_message(call.message.chat.id,
                              "💰 حركات الصندوق\n\n📅 كم يوم تريد؟ (اكتب رقم من 1 إلى 365)\n\nمثال: 7 لآخر أسبوع",
                              reply_markup=self.get_cancel_keyboard())
            self.bot.register_next_step_handler(msg, self.process_days_for_box_movements)

        @self.bot.callback_query_handler(func=lambda call: call.data == "box_date")
        def box_date_callback(call):
            """حركات الصندوق بتاريخ محدد"""
            self.bot.answer_callback_query(call.id, "📆 تاريخ محدد...")

            msg = self.bot.send_message(call.message.chat.id,
                              "💰 حركات الصندوق\n\n📆 اكتب التاريخ بالصيغة: DD-MM-YYYY\n\nمثال: 04-09-2025",
                              reply_markup=self.get_cancel_keyboard())
            self.bot.register_next_step_handler(msg, self.process_date_for_box_movements)

        @self.bot.callback_query_handler(func=lambda call: call.data == "main_menu")
        def main_menu_callback(call):
            """العودة للقائمة الرئيسية"""
            self.bot.answer_callback_query(call.id, "🏠 القائمة الرئيسية")
            self.bot.send_message(call.message.chat.id, "🏠 القائمة الرئيسية:", reply_markup=self.get_main_keyboard())

        @self.bot.callback_query_handler(func=lambda call: call.data == "cancel")
        def cancel_callback(call):
            """إلغاء العملية الحالية"""
            self.bot.answer_callback_query(call.id, "❌ تم الإلغاء")
            self.bot.send_message(call.message.chat.id, "❌ تم إلغاء العملية\n\n🏠 القائمة الرئيسية:", reply_markup=self.get_main_keyboard())

        # معالج الرسائل النصية للإلغاء والقائمة الرئيسية
        @self.bot.message_handler(func=lambda message: message.text in ["إلغاء", "🏠 القائمة الرئيسية"])
        def cancel_and_menu_text_handler(message):
            """معالج نص الإلغاء والقائمة الرئيسية"""
            if message.text == "إلغاء":
                self.bot.send_message(message.chat.id, "❌ تم إلغاء العملية",
                                    reply_markup=types.ReplyKeyboardRemove())
            else:
                self.bot.send_message(message.chat.id, "🏠 القائمة الرئيسية",
                                    reply_markup=types.ReplyKeyboardRemove())

            self.bot.send_message(message.chat.id, "اختر من القائمة:", reply_markup=self.get_main_keyboard())

    def get_cancel_keyboard(self):
        """لوحة مفاتيح الإلغاء"""
        markup = types.ReplyKeyboardMarkup(resize_keyboard=True, one_time_keyboard=True)
        markup.add(types.KeyboardButton("إلغاء"))
        markup.add(types.KeyboardButton("🏠 القائمة الرئيسية"))
        return markup

    def get_inline_cancel_keyboard(self):
        """لوحة مفاتيح الإلغاء المدمجة"""
        markup = types.InlineKeyboardMarkup()
        markup.add(
            types.InlineKeyboardButton("❌ إلغاء", callback_data="cancel"),
            types.InlineKeyboardButton("🏠 القائمة الرئيسية", callback_data="main_menu")
        )
        return markup

    def get_main_keyboard(self):
        """لوحة المفاتيح الرئيسية الاحترافية"""
        markup = types.InlineKeyboardMarkup()

        # الصف الأول - التقارير الأساسية
        markup.add(
            types.InlineKeyboardButton("📊 ملخص اليوم", callback_data="daily_summary"),
            types.InlineKeyboardButton("� فواتير مفصلة", callback_data="detailed_invoices")
        )

        # الصف الثاني - المالية
        markup.add(
            types.InlineKeyboardButton("� حركات الصندوق", callback_data="box_movements"),
            types.InlineKeyboardButton("💸 تقارير المصروفات", callback_data="expenses_reports")
        )

        # الصف الثالث - التحليلات
        markup.add(
            types.InlineKeyboardButton("📈 تقارير المديونيات", callback_data="debt_reports"),
            types.InlineKeyboardButton("🏢 أداء الشركات", callback_data="companies_performance")
        )

        # الصف الرابع - البحث والإعدادات
        markup.add(
            types.InlineKeyboardButton("🔍 بحث متقدم", callback_data="advanced_search"),
            types.InlineKeyboardButton("⚙️ الإعدادات", callback_data="settings")
        )

        return markup

    def process_days_for_detailed_invoices(self, message):
        """معالجة عدد الأيام للفواتير المفصلة"""
        if message.text == "إلغاء":
            self.bot.send_message(message.chat.id, "❌ تم إلغاء العملية", reply_markup=types.ReplyKeyboardRemove())
            return
        
        try:
            days = int(message.text)
            if days <= 0 or days > 365:
                self.bot.send_message(message.chat.id, "❌ يرجى إدخال رقم بين 1 و 365", reply_markup=types.ReplyKeyboardRemove())
                return
            
            self.bot.send_message(message.chat.id, f"🔍 جاري البحث عن فواتير آخر {days} يوم...", reply_markup=types.ReplyKeyboardRemove())
            self.get_detailed_invoices_report(message.chat.id, days)
            
        except ValueError:
            self.bot.send_message(message.chat.id, "❌ يرجى إدخال رقم صحيح", reply_markup=types.ReplyKeyboardRemove())
        except Exception as e:
            logging.error(f"خطأ في معالجة الأيام: {e}")
            self.bot.send_message(message.chat.id, f"❌ خطأ: {e}", reply_markup=types.ReplyKeyboardRemove())

    def process_days_for_expenses(self, message):
        """معالجة عدد الأيام لتقارير المصروفات"""
        if message.text == "إلغاء":
            self.bot.send_message(message.chat.id, "❌ تم إلغاء العملية", reply_markup=types.ReplyKeyboardRemove())
            return

        try:
            days = int(message.text)
            if days <= 0 or days > 365:
                self.bot.send_message(message.chat.id, "❌ يرجى إدخال رقم بين 1 و 365", reply_markup=types.ReplyKeyboardRemove())
                return

            self.bot.send_message(message.chat.id, f"💸 جاري البحث عن مصروفات آخر {days} يوم...", reply_markup=types.ReplyKeyboardRemove())
            self.get_expenses_report(message.chat.id, days)

        except ValueError:
            self.bot.send_message(message.chat.id, "❌ يرجى إدخال رقم صحيح", reply_markup=types.ReplyKeyboardRemove())
        except Exception as e:
            logging.error(f"خطأ في معالجة أيام المصروفات: {e}")
            self.bot.send_message(message.chat.id, f"❌ خطأ: {e}", reply_markup=types.ReplyKeyboardRemove())

    def process_days_for_box_movements(self, message):
        """معالجة عدد الأيام لحركات الصندوق"""
        if message.text == "إلغاء":
            self.bot.send_message(message.chat.id, "❌ تم إلغاء العملية", reply_markup=types.ReplyKeyboardRemove())
            return

        try:
            days = int(message.text)
            if days <= 0 or days > 365:
                self.bot.send_message(message.chat.id, "❌ يرجى إدخال رقم بين 1 و 365", reply_markup=types.ReplyKeyboardRemove())
                return

            self.bot.send_message(message.chat.id, f"💰 جاري البحث عن حركات الصندوق آخر {days} يوم...", reply_markup=types.ReplyKeyboardRemove())
            self.get_box_movements_report(message.chat.id, days)

        except ValueError:
            self.bot.send_message(message.chat.id, "❌ يرجى إدخال رقم صحيح", reply_markup=types.ReplyKeyboardRemove())
        except Exception as e:
            logging.error(f"خطأ في معالجة أيام حركات الصندوق: {e}")
            self.bot.send_message(message.chat.id, f"❌ خطأ: {e}", reply_markup=types.ReplyKeyboardRemove())

    def process_days_for_companies_performance(self, message):
        """معالجة عدد الأيام لأداء الشركات"""
        if message.text == "إلغاء":
            self.bot.send_message(message.chat.id, "❌ تم إلغاء العملية", reply_markup=types.ReplyKeyboardRemove())
            return

        try:
            days = int(message.text)
            if days <= 0 or days > 365:
                self.bot.send_message(message.chat.id, "❌ يرجى إدخال رقم بين 1 و 365", reply_markup=types.ReplyKeyboardRemove())
                return

            self.bot.send_message(message.chat.id, f"🏢 جاري تحليل أداء الشركات آخر {days} يوم...", reply_markup=types.ReplyKeyboardRemove())
            self.get_companies_performance_report(message.chat.id, days)

        except ValueError:
            self.bot.send_message(message.chat.id, "❌ يرجى إدخال رقم صحيح", reply_markup=types.ReplyKeyboardRemove())
        except Exception as e:
            logging.error(f"خطأ في معالجة أيام أداء الشركات: {e}")
            self.bot.send_message(message.chat.id, f"❌ خطأ: {e}", reply_markup=types.ReplyKeyboardRemove())

    def process_date_for_detailed_invoices(self, message):
        """معالجة التاريخ المحدد للفواتير المفصلة"""
        if message.text == "إلغاء":
            self.bot.send_message(message.chat.id, "❌ تم إلغاء العملية", reply_markup=types.ReplyKeyboardRemove())
            return

        try:
            # تحويل التاريخ من DD-MM-YYYY إلى datetime
            date_parts = message.text.split('-')
            if len(date_parts) != 3:
                raise ValueError("صيغة التاريخ غير صحيحة")

            day, month, year = map(int, date_parts)
            target_date = datetime(year, month, day)

            self.bot.send_message(message.chat.id, f"🔍 جاري البحث عن فواتير يوم {target_date.strftime('%d-%m-%Y')}...", reply_markup=types.ReplyKeyboardRemove())
            self.get_detailed_invoices_by_date(message.chat.id, target_date)

        except ValueError as e:
            self.bot.send_message(message.chat.id, "❌ صيغة التاريخ غير صحيحة. استخدم: DD-MM-YYYY\nمثال: 04-09-2025", reply_markup=types.ReplyKeyboardRemove())
        except Exception as e:
            logging.error(f"خطأ في معالجة التاريخ للفواتير: {e}")
            self.bot.send_message(message.chat.id, f"❌ خطأ: {e}", reply_markup=types.ReplyKeyboardRemove())

    def process_date_for_expenses(self, message):
        """معالجة التاريخ المحدد للمصروفات"""
        if message.text == "إلغاء":
            self.bot.send_message(message.chat.id, "❌ تم إلغاء العملية", reply_markup=types.ReplyKeyboardRemove())
            return

        try:
            date_parts = message.text.split('-')
            if len(date_parts) != 3:
                raise ValueError("صيغة التاريخ غير صحيحة")

            day, month, year = map(int, date_parts)
            target_date = datetime(year, month, day)

            self.bot.send_message(message.chat.id, f"💸 جاري البحث عن مصروفات يوم {target_date.strftime('%d-%m-%Y')}...", reply_markup=types.ReplyKeyboardRemove())
            self.get_expenses_by_date(message.chat.id, target_date)

        except ValueError as e:
            self.bot.send_message(message.chat.id, "❌ صيغة التاريخ غير صحيحة. استخدم: DD-MM-YYYY\nمثال: 04-09-2025", reply_markup=types.ReplyKeyboardRemove())
        except Exception as e:
            logging.error(f"خطأ في معالجة التاريخ للمصروفات: {e}")
            self.bot.send_message(message.chat.id, f"❌ خطأ: {e}", reply_markup=types.ReplyKeyboardRemove())

    def process_date_for_box_movements(self, message):
        """معالجة التاريخ المحدد لحركات الصندوق"""
        if message.text == "إلغاء":
            self.bot.send_message(message.chat.id, "❌ تم إلغاء العملية", reply_markup=types.ReplyKeyboardRemove())
            return

        try:
            date_parts = message.text.split('-')
            if len(date_parts) != 3:
                raise ValueError("صيغة التاريخ غير صحيحة")

            day, month, year = map(int, date_parts)
            target_date = datetime(year, month, day)

            self.bot.send_message(message.chat.id, f"💰 جاري البحث عن حركات الصندوق يوم {target_date.strftime('%d-%m-%Y')}...", reply_markup=types.ReplyKeyboardRemove())
            self.get_box_movements_by_date(message.chat.id, target_date)

        except ValueError as e:
            self.bot.send_message(message.chat.id, "❌ صيغة التاريخ غير صحيحة. استخدم: DD-MM-YYYY\nمثال: 04-09-2025", reply_markup=types.ReplyKeyboardRemove())
        except Exception as e:
            logging.error(f"خطأ في معالجة التاريخ لحركات الصندوق: {e}")
            self.bot.send_message(message.chat.id, f"❌ خطأ: {e}", reply_markup=types.ReplyKeyboardRemove())

    def process_date_for_companies_performance(self, message):
        """معالجة التاريخ المحدد لأداء الشركات"""
        if message.text == "إلغاء":
            self.bot.send_message(message.chat.id, "❌ تم إلغاء العملية", reply_markup=types.ReplyKeyboardRemove())
            return

        try:
            date_parts = message.text.split('-')
            if len(date_parts) != 3:
                raise ValueError("صيغة التاريخ غير صحيحة")

            day, month, year = map(int, date_parts)
            target_date = datetime(year, month, day)

            self.bot.send_message(message.chat.id, f"🏢 جاري تحليل أداء الشركات يوم {target_date.strftime('%d-%m-%Y')}...", reply_markup=types.ReplyKeyboardRemove())
            self.get_companies_performance_by_date(message.chat.id, target_date)

        except ValueError as e:
            self.bot.send_message(message.chat.id, "❌ صيغة التاريخ غير صحيحة. استخدم: DD-MM-YYYY\nمثال: 04-09-2025", reply_markup=types.ReplyKeyboardRemove())
        except Exception as e:
            logging.error(f"خطأ في معالجة التاريخ لأداء الشركات: {e}")
            self.bot.send_message(message.chat.id, f"❌ خطأ: {e}", reply_markup=types.ReplyKeyboardRemove())

    def get_detailed_invoices_report(self, chat_id, days):
        """الحصول على تقرير الفواتير المفصل - بدون حد للرسائل"""
        try:
            conn = self.get_primary_connection()
            cursor = conn.cursor()
            
            # الحصول على جميع بيانات الفواتير
            cursor.execute("""
                SELECT 
                    company_name, fatora_serial, fatora_date, user_name, the_buyer, 
                    the_store, good_name, good_code, good_quentity, unit_price, 
                    good_total_price, good_kind, is_payed, is_supplied, aysal_serial,
                    is_mortadat, store_mortadat_quen, mortadat_status, supplied_date,
                    total_selling_price, total_rb7, good_category, mortg3at_serial,
                    al_lesta_price, gomla_discount_percentage, printing_gomla_price,
                    gomla_price_is_updated, gomla_old_price
                FROM buying_fatora
                WHERE fatora_date >= DATEADD(DAY, -?, GETDATE())
                ORDER BY fatora_serial DESC, good_name
            """, (days,))
            
            all_items = cursor.fetchall()
            conn.close()
            
            if not all_items:
                self.bot.send_message(chat_id, f"📋 لا توجد فواتير في آخر {days} يوم")
                return
            
            # تجميع البيانات حسب الفاتورة
            invoices_dict = {}
            for item in all_items:
                serial = int(item[1]) if item[1] else 0
                if serial not in invoices_dict:
                    invoices_dict[serial] = {
                        'info': {
                            'company': item[0],
                            'serial': serial,
                            'date': item[2],
                            'user': item[3],
                            'buyer': item[4],
                            'store': item[5],
                            'payment_status': item[12],
                            'supply_status': item[13],
                            'receipt_number': item[14]
                        },
                        'items': []
                    }
                
                invoices_dict[serial]['items'].append({
                    'name': item[6],
                    'code': int(item[7]) if item[7] else 0,
                    'quantity': float(item[8]) if item[8] else 0,
                    'unit_price': float(item[9]) if item[9] else 0,
                    'total_price': float(item[10]) if item[10] else 0,
                    'category': item[11],
                    'is_paid': item[12],
                    'is_supplied': item[13],
                    'is_returned': item[15] if item[15] else 0,
                    'return_quantity': float(item[16]) if item[16] else 0,
                    'return_status': item[17]
                })
            
            # إرسال التقرير الكامل بدون حدود
            self.send_complete_invoice_report(chat_id, invoices_dict, days)
            
        except Exception as e:
            logging.error(f"خطأ في الحصول على التقرير المفصل: {e}")
            self.bot.send_message(chat_id, f"❌ خطأ في جلب التقرير: {e}")

    def get_detailed_invoices_by_date(self, chat_id, target_date):
        """الحصول على الفواتير المفصلة بتاريخ محدد"""
        try:
            conn = self.get_primary_connection()
            cursor = conn.cursor()

            # الحصول على جميع بيانات الفواتير لليوم المحدد
            cursor.execute("""
                SELECT
                    company_name, fatora_serial, fatora_date, user_name, the_buyer,
                    the_store, good_name, good_code, good_quentity, unit_price,
                    good_total_price, good_kind, is_payed, is_supplied, aysal_serial,
                    is_mortadat, store_mortadat_quen, mortadat_status
                FROM buying_fatora
                WHERE CAST(fatora_date AS DATE) = CAST(? AS DATE)
                ORDER BY fatora_serial DESC, good_name
            """, (target_date,))

            all_items = cursor.fetchall()
            conn.close()

            if not all_items:
                self.bot.send_message(chat_id, f"📋 لا توجد فواتير في يوم {target_date.strftime('%d-%m-%Y')}")
                return

            # تجميع البيانات حسب الفاتورة
            invoices_dict = {}
            for item in all_items:
                serial = int(item[1]) if item[1] else 0
                if serial not in invoices_dict:
                    invoices_dict[serial] = {
                        'info': {
                            'company': item[0],
                            'serial': serial,
                            'date': item[2],
                            'user': item[3],
                            'buyer': item[4],
                            'store': item[5],
                            'payment_status': item[12],
                            'supply_status': item[13],
                            'receipt_number': item[14]
                        },
                        'items': []
                    }

                invoices_dict[serial]['items'].append({
                    'name': item[6],
                    'code': int(item[7]) if item[7] else 0,
                    'quantity': float(item[8]) if item[8] else 0,
                    'unit_price': float(item[9]) if item[9] else 0,
                    'total_price': float(item[10]) if item[10] else 0,
                    'category': item[11],
                    'is_paid': item[12],
                    'is_supplied': item[13],
                    'is_returned': item[15] if item[15] else 0,
                    'return_quantity': float(item[16]) if item[16] else 0,
                    'return_status': item[17]
                })

            # إرسال التقرير الكامل
            self.send_complete_report_by_date(chat_id, invoices_dict, target_date, "فواتير")

        except Exception as e:
            logging.error(f"خطأ في الحصول على الفواتير بالتاريخ: {e}")
            self.bot.send_message(chat_id, f"❌ خطأ في جلب الفواتير: {e}")

    def get_expenses_by_date(self, chat_id, target_date):
        """الحصول على المصروفات بتاريخ محدد"""
        try:
            conn = self.get_primary_connection()
            cursor = conn.cursor()

            # الحصول على المصروفات لليوم المحدد
            cursor.execute("""
                SELECT
                    company_name_doing_aysal,
                    aysal_serial,
                    the_mony,
                    the_reson,
                    the_date,
                    company_name
                FROM masrofat
                WHERE CAST(the_date AS DATE) = CAST(? AS DATE)
                ORDER BY the_date DESC
            """, (target_date,))

            expenses = cursor.fetchall()
            conn.close()

            if not expenses:
                self.bot.send_message(chat_id, f"💸 لا توجد مصروفات في يوم {target_date.strftime('%d-%m-%Y')}")
                return

            # تحليل المصروفات
            total_expenses = sum(float(row[2] or 0) for row in expenses)

            # الرسالة الرئيسية
            header = f"💸 مصروفات يوم {target_date.strftime('%d-%m-%Y')}\n"
            header += "=" * 40 + "\n\n"
            header += f"📊 إجمالي المصروفات: {total_expenses:,.0f} ج\n"
            header += f"📋 عدد المصروفات: {len(expenses):,}\n\n"

            self.bot.send_message(chat_id, header)

            # تفاصيل جميع المصروفات
            details_msg = f"📋 تفاصيل جميع المصروفات ({len(expenses)} مصروف):\n"
            details_msg += "=" * 35 + "\n\n"

            for i, expense in enumerate(expenses, 1):
                company = expense[0] or expense[5] or "غير محدد"
                serial = int(expense[1]) if expense[1] else 0
                amount = float(expense[2] or 0)
                reason = expense[3] or "غير محدد"
                date = expense[4]

                details_msg += f"{i}. مصروف #{serial}\n"
                details_msg += f"   🏢 الشركة: {company}\n"
                details_msg += f"   💰 المبلغ: {amount:,.0f} ج\n"
                details_msg += f"   📝 السبب: {reason}\n"
                details_msg += f"   📅 الوقت: {date.strftime('%H:%M:%S') if date else 'غير محدد'}\n\n"

                # إرسال كل 5 مصروفات
                if i % 5 == 0 or i == len(expenses):
                    self.bot.send_message(chat_id, details_msg)
                    details_msg = ""
                    time.sleep(1)

            if details_msg:
                self.bot.send_message(chat_id, details_msg)

        except Exception as e:
            logging.error(f"خطأ في المصروفات بالتاريخ: {e}")
            self.bot.send_message(chat_id, f"❌ خطأ في جلب المصروفات: {e}")

    def send_complete_invoice_report(self, chat_id, invoices_dict, days):
        """إرسال التقرير الكامل للفواتير - جميع الفواتير بدون حدود"""
        try:
            total_invoices = len(invoices_dict)
            total_items = sum(len(inv['items']) for inv in invoices_dict.values())
            total_value = sum(
                sum(item['total_price'] for item in inv['items'])
                for inv in invoices_dict.values()
            )
            
            # إحصائيات مفصلة
            paid_invoices = 0
            supplied_invoices = 0
            returned_items = 0
            pending_payment = 0
            pending_supply = 0
            
            for inv in invoices_dict.values():
                if inv['info']['payment_status'] == 'تم السداد':
                    paid_invoices += 1
                else:
                    pending_payment += 1
                    
                if inv['info']['supply_status'] == ' تم التسليم':
                    supplied_invoices += 1
                else:
                    pending_supply += 1
                    
                returned_items += sum(1 for item in inv['items'] if item['is_returned'])
            
            # الرسالة الرئيسية
            header = f"📊 تقرير الفواتير الكامل - آخر {days} يوم\n"
            header += "=" * 40 + "\n\n"
            header += f"📋 إجمالي الفواتير: {total_invoices:,}\n"
            header += f"📦 إجمالي الأصناف: {total_items:,}\n"
            header += f"💰 إجمالي القيمة: {total_value:,.0f} ج\n\n"
            
            header += "💳 حالة الدفع:\n"
            header += f"  ✅ مدفوع: {paid_invoices} فاتورة\n"
            header += f"  ⏳ معلق: {pending_payment} فاتورة\n\n"
            
            header += "🚚 حالة التسليم:\n"
            header += f"  ✅ مسلم: {supplied_invoices} فاتورة\n"
            header += f"  ⏳ معلق: {pending_supply} فاتورة\n\n"
            
            header += f"↩️ مرتدات: {returned_items} صنف\n\n"
            header += f"📋 سيتم إرسال جميع الـ {total_invoices} فاتورة..."
            
            self.bot.send_message(chat_id, header)
            
            # إرسال جميع الفواتير بدون حدود
            count = 0
            for serial, invoice_data in invoices_dict.items():
                count += 1
                try:
                    self.send_single_invoice_details(chat_id, invoice_data, count, total_invoices)
                    time.sleep(1)  # تجنب حد الإرسال
                except Exception as e:
                    logging.error(f"خطأ في إرسال فاتورة {serial}: {e}")
                    continue
            
            self.bot.send_message(chat_id, f"✅ تم إرسال جميع الـ {total_invoices} فاتورة بالكامل!", reply_markup=self.get_main_keyboard())
            
        except Exception as e:
            logging.error(f"خطأ في إرسال التقرير الكامل: {e}")
            self.bot.send_message(chat_id, f"❌ خطأ في إرسال التقرير: {e}")

    def send_complete_report_by_date(self, chat_id, invoices_dict, target_date, report_type):
        """إرسال التقرير الكامل بالتاريخ المحدد"""
        try:
            total_invoices = len(invoices_dict)
            total_items = sum(len(inv['items']) for inv in invoices_dict.values())
            total_value = sum(
                sum(item['total_price'] for item in inv['items'])
                for inv in invoices_dict.values()
            )

            # الرسالة الرئيسية
            header = f"📊 {report_type} يوم {target_date.strftime('%d-%m-%Y')}\n"
            header += "=" * 40 + "\n\n"
            header += f"📋 إجمالي الفواتير: {total_invoices:,}\n"
            header += f"📦 إجمالي الأصناف: {total_items:,}\n"
            header += f"💰 إجمالي القيمة: {total_value:,.0f} ج\n\n"
            header += f"📋 سيتم إرسال جميع الـ {total_invoices} فاتورة..."

            self.bot.send_message(chat_id, header)

            # إرسال جميع الفواتير
            count = 0
            for serial, invoice_data in invoices_dict.items():
                count += 1
                try:
                    self.send_single_invoice(chat_id, invoice_data, count, total_invoices)
                    time.sleep(1)
                except Exception as e:
                    logging.error(f"خطأ في إرسال فاتورة {serial}: {e}")
                    continue

            self.bot.send_message(chat_id, f"✅ تم إرسال جميع الـ {total_invoices} فاتورة بالكامل!")

        except Exception as e:
            logging.error(f"خطأ في إرسال التقرير بالتاريخ: {e}")
            self.bot.send_message(chat_id, f"❌ خطأ في إرسال التقرير: {e}")

    def send_single_invoice(self, chat_id, invoice_data, count, total):
        """إرسال فاتورة واحدة مفصلة"""
        try:
            info = invoice_data['info']
            items = invoice_data['items']

            # حساب إجمالي الفاتورة
            total_amount = sum(item['total_price'] for item in items)

            # رسالة الفاتورة
            msg = f"🧾 الفاتورة #{count}/{total} - رقم {info['serial']}\n"
            msg += "=" * 35 + "\n"
            msg += f"🏢 الشركة: {info['company'] or 'غير محدد'}\n"
            msg += f"📅 التاريخ: {info['date'].strftime('%Y-%m-%d %H:%M') if info['date'] else 'غير محدد'}\n"
            msg += f"👤 المشتري: {info['buyer'] or 'غير محدد'}\n"
            msg += f"🏪 المخزن: {info['store'] or 'غير محدد'}\n"

            # حالة الدفع والتسليم
            payment_icon = "✅" if info['payment_status'] == 'تم السداد' else "⏳"
            supply_icon = "✅" if info['supply_status'] == ' تم التسليم' else "⏳"

            msg += f"💳 الدفع: {payment_icon} {info['payment_status'] or 'معلق'}\n"
            msg += f"🚚 التسليم: {supply_icon} {info['supply_status'] or 'معلق'}\n"
            msg += f"💰 إجمالي الفاتورة: {total_amount:,.0f} ج\n\n"

            # أصناف الفاتورة
            msg += f"📦 أصناف الفاتورة ({len(items)} صنف):\n"
            msg += "=" * 35 + "\n\n"

            for i, item in enumerate(items, 1):
                msg += f"{i}. {item['name'] or 'غير محدد'}\n"
                msg += f"   🔢 كود: {item['code']}\n"
                msg += f"   📊 كمية: {item['quantity']:,.0f}\n"
                msg += f"   💵 سعر الوحدة: {item['unit_price']:,.0f} ج\n"
                msg += f"   💰 الإجمالي: {item['total_price']:,.0f} ج\n"
                msg += f"   🏷️ الفئة: {item['category'] or 'غير محدد'}\n"

                # حالة الصنف
                status_icons = ""
                if item['is_paid'] == 'تم السداد':
                    status_icons += "💳"
                if item['is_supplied'] == ' تم التسليم':
                    status_icons += "🚚"
                if item['is_returned']:
                    status_icons += f"↩️({item['return_quantity']:,.0f})"

                if status_icons:
                    msg += f"   📋 الحالة: {status_icons}\n"

                msg += "\n"

            self.bot.send_message(chat_id, msg)

        except Exception as e:
            logging.error(f"خطأ في إرسال فاتورة مفردة: {e}")
            self.bot.send_message(chat_id, f"❌ خطأ في عرض الفاتورة {info.get('serial', 'غير محدد')}")

    def get_tracking_connection(self):
        """الحصول على اتصال قاعدة بيانات التتبع على الخادم الاحتياطي فقط"""
        try:
            # الاتصال بالخادم الاحتياطي فقط - ممنوع الكتابة على الخادم الأساسي
            conn_str = (
                "DRIVER={ODBC Driver 17 for SQL Server};"
                f"SERVER={TRACKING_SERVER};"
                f"DATABASE={TRACKING_DATABASE};"
                f"UID={TRACKING_USERNAME};"
                f"PWD={TRACKING_PASSWORD};"
                "TrustServerCertificate=yes;"
            )

            connection = pyodbc.connect(conn_str, timeout=10)
            logging.info("✅ تم الاتصال بقاعدة التتبع على الخادم الاحتياطي")
            return connection

        except Exception as e:
            logging.error(f"❌ فشل الاتصال بقاعدة بيانات التتبع على الخادم الاحتياطي: {e}")
            logging.error("🚨 لن يتم حفظ بيانات التتبع - الخادم الاحتياطي غير متاح")
            return None

    def setup_tracking_tables(self):
        """إنشاء جداول التتبع (تم إنشاؤها مسبقاً)"""
        # الجداول تم إنشاؤها بالفعل باستخدام create_tracking_database.py
        logging.info("جداول التتبع موجودة بالفعل")
        return True

    def load_authorized_users(self):
        """تحميل المستخدمين المصرح لهم من قاعدة البيانات"""
        try:
            conn = self.get_tracking_connection()
            if not conn:
                return AUTHORIZED_USERS

            cursor = conn.cursor()
            cursor.execute("SELECT user_id FROM authorized_users WHERE is_active = 1")
            users = [row[0] for row in cursor.fetchall()]
            cursor.close()

            return users if users else AUTHORIZED_USERS

        except Exception as e:
            logging.error(f"خطأ في تحميل المستخدمين المصرح لهم: {e}")
            return AUTHORIZED_USERS

    def send_invoice_notification_smart(self, invoice_data):
        """إرسال إشعار فاتورة ذكي مع التفاصيل الكاملة"""
        try:
            # تنسيق البيانات
            serial = invoice_data[0]
            company = invoice_data[1] or 'غير محدد'
            date = invoice_data[2]
            user = invoice_data[3] or 'غير محدد'
            buyer = invoice_data[4] or 'غير محدد'
            store = invoice_data[5] or 'غير محدد'
            total_amount = invoice_data[6] or 0
            items_count = invoice_data[7] or 0
            payment_status = 'تم السداد' if invoice_data[8] == 'تم السداد' else 'معلق'
            supply_status = 'تم التسليم' if invoice_data[9] == ' تم التسليم' else 'معلق'

            # رموز الحالة
            payment_icon = "✅" if payment_status == 'تم السداد' else "⏳"
            supply_icon = "✅" if supply_status == 'تم التسليم' else "⏳"

            # تنسيق الرسالة
            msg = f"🆕 فاتورة جديدة!\n"
            msg += "=" * 30 + "\n"
            msg += f"🧾 رقم الفاتورة: {serial}\n"
            msg += f"🏢 الشركة: {company}\n"
            msg += f"👤 المشتري: {buyer}\n"
            msg += f"👨‍💼 المستخدم: {user}\n"
            msg += f"🏪 المخزن: {store}\n"
            msg += f"📅 التاريخ: {date.strftime('%Y-%m-%d %H:%M') if date else 'غير محدد'}\n"
            msg += f"📦 عدد الأصناف: {items_count}\n"
            msg += f"💰 إجمالي المبلغ: {total_amount:,.0f} ج\n"
            msg += f"💳 الدفع: {payment_icon} {payment_status}\n"
            msg += f"🚚 التسليم: {supply_icon} {supply_status}"

            # إرسال للمستخدمين المصرح لهم
            for user_id in NOTIFICATION_SUBSCRIBERS:
                try:
                    self.bot.send_message(user_id, msg)
                except Exception as e:
                    logging.error(f"خطأ في إرسال إشعار فاتورة للمستخدم {user_id}: {e}")

        except Exception as e:
            logging.error(f"خطأ في إرسال إشعار فاتورة ذكي: {e}")

    def send_expense_notification_smart(self, expense_data):
        """إرسال إشعار مصروف ذكي مع التفاصيل الكاملة"""
        try:
            # تنسيق البيانات
            serial = expense_data[0] or 'غير محدد'
            company_doing = expense_data[1] or 'غير محدد'
            company = expense_data[2] or 'غير محدد'
            amount = expense_data[3] or 0
            reason = expense_data[4] or 'غير محدد'
            date = expense_data[5]
            recipient = expense_data[6] or 'غير محدد'
            is_done = expense_data[7]
            sarf_date = expense_data[8]
            casher = expense_data[9] or 'غير محدد'
            main_account = expense_data[10] or 'غير محدد'
            sub_account = expense_data[11] or 'غير محدد'

            # رموز الحالة
            status_icon = "✅" if is_done == 1 else "⏳"
            status_text = "تم الصرف" if is_done == 1 else "معلق"

            # تنسيق الرسالة
            msg = f"💸 مصروف جديد!\n"
            msg += "=" * 30 + "\n"
            msg += f"🧾 رقم المصروف: {serial}\n"
            msg += f"🏢 الشركة المنفذة: {company_doing}\n"
            msg += f"🏪 الشركة: {company}\n"
            msg += f"💰 المبلغ: {amount:,.0f} ج\n"
            msg += f"📝 السبب: {reason}\n"
            msg += f"👤 المستلم: {recipient}\n"
            msg += f"💳 الصراف: {casher}\n"
            msg += f"📊 الحساب الرئيسي: {main_account}\n"
            msg += f"📋 الحساب الفرعي: {sub_account}\n"
            msg += f"📅 تاريخ الطلب: {date.strftime('%Y-%m-%d %H:%M') if date else 'غير محدد'}\n"
            if sarf_date:
                msg += f"📅 تاريخ الصرف: {sarf_date.strftime('%Y-%m-%d %H:%M')}\n"
            msg += f"📊 الحالة: {status_icon} {status_text}"

            # إرسال للمستخدمين المصرح لهم
            for user_id in NOTIFICATION_SUBSCRIBERS:
                try:
                    self.bot.send_message(user_id, msg)
                except Exception as e:
                    logging.error(f"خطأ في إرسال إشعار مصروف للمستخدم {user_id}: {e}")

        except Exception as e:
            logging.error(f"خطأ في إرسال إشعار مصروف ذكي: {e}")

    def load_last_ids(self):
        """تحميل آخر تواريخ فحص من القاعدة الأساسية"""
        try:
            # جلب آخر البيانات من القاعدة الأساسية مباشرة
            conn = self.get_primary_connection()
            if not conn:
                logging.warning("فشل الاتصال بالقاعدة الأساسية، استخدام التواريخ الافتراضية")
                return

            cursor = conn.cursor()

            # آخر فاتورة من القاعدة الأساسية
            cursor.execute("SELECT MAX(fatora_date) FROM buying_fatora")
            result = cursor.fetchone()
            if result and result[0]:
                # تقليل 5 دقائق للتأكد من عدم فقدان أي بيانات
                self.last_invoice_check = result[0] - timedelta(minutes=5)

            # آخر مصروف من القاعدة الأساسية
            cursor.execute("SELECT MAX(the_date) FROM masrofat")
            result = cursor.fetchone()
            if result and result[0]:
                # تقليل 5 دقائق للتأكد من عدم فقدان أي بيانات
                self.last_expense_check = result[0] - timedelta(minutes=5)

            cursor.close()
            conn.close()
            logging.info(f"تم تحميل آخر تواريخ من القاعدة الأساسية: فواتير={self.last_invoice_check}, مصروفات={self.last_expense_check}")

        except Exception as e:
            logging.error(f"خطأ في تحميل آخر تواريخ: {e}")

    def save_invoice_to_tracking(self, invoice):
        """حفظ فاتورة في جدول التتبع على الخادم الاحتياطي فقط - جميع الأعمدة"""
        try:
            tracking_conn = self.get_tracking_connection()
            if not tracking_conn:
                logging.warning("⚠️ لا يمكن حفظ الفاتورة - الخادم الاحتياطي غير متاح")
                return False

            cursor = tracking_conn.cursor()

            # التحقق من وجود الفاتورة أولاً
            cursor.execute("SELECT COUNT(*) FROM invoice_tracking WHERE fatora_serial = ?", (str(invoice[1]),))
            exists = cursor.fetchone()[0] > 0

            if not exists:
                # إدراج فاتورة جديدة فقط - جميع الأعمدة
                cursor.execute("""
                    INSERT INTO invoice_tracking
                    (company_name, fatora_serial, fatora_date, user_name, the_buyer, the_store,
                     good_name, good_code, good_quentity, unit_price, good_total_price, good_kind,
                     is_payed, is_supplied, aysal_serial, is_mortadat, store_mortadat_quen,
                     mortadat_status, supplied_date, total_selling_price, total_rb7, good_category,
                     mortg3at_serial, al_lesta_price, gomla_discount_percentage, printing_gomla_price,
                     gomla_price_is_updated, gomla_old_price, sent_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, GETDATE())
                """, (
                    invoice[0] if len(invoice) > 0 else '',  # company_name
                    invoice[1] if len(invoice) > 1 else '',  # fatora_serial
                    invoice[2] if len(invoice) > 2 else None,  # fatora_date
                    invoice[3] if len(invoice) > 3 else '',  # user_name
                    invoice[4] if len(invoice) > 4 else '',  # the_buyer
                    invoice[5] if len(invoice) > 5 else '',  # the_store
                    invoice[6] if len(invoice) > 6 else '',  # good_name
                    invoice[7] if len(invoice) > 7 else '',  # good_code
                    invoice[8] if len(invoice) > 8 else 0,   # good_quentity
                    invoice[9] if len(invoice) > 9 else 0,   # unit_price
                    invoice[10] if len(invoice) > 10 else 0, # good_total_price
                    invoice[11] if len(invoice) > 11 else '', # good_kind
                    invoice[12] if len(invoice) > 12 else '', # is_payed
                    invoice[13] if len(invoice) > 13 else '', # is_supplied
                    invoice[14] if len(invoice) > 14 else '', # aysal_serial
                    invoice[15] if len(invoice) > 15 else '', # is_mortadat
                    invoice[16] if len(invoice) > 16 else 0,  # store_mortadat_quen
                    invoice[17] if len(invoice) > 17 else '', # mortadat_status
                    invoice[18] if len(invoice) > 18 else None, # supplied_date
                    invoice[19] if len(invoice) > 19 else 0,  # total_selling_price
                    invoice[20] if len(invoice) > 20 else 0,  # total_rb7
                    invoice[21] if len(invoice) > 21 else '', # good_category
                    invoice[22] if len(invoice) > 22 else '', # mortg3at_serial
                    invoice[23] if len(invoice) > 23 else 0,  # al_lesta_price
                    invoice[24] if len(invoice) > 24 else 0,  # gomla_discount_percentage
                    invoice[25] if len(invoice) > 25 else 0,  # printing_gomla_price
                    invoice[26] if len(invoice) > 26 else '', # gomla_price_is_updated
                    invoice[27] if len(invoice) > 27 else 0   # gomla_old_price
                ))

                tracking_conn.commit()
                logging.info(f"✅ تم حفظ فاتورة {invoice[1]} في قاعدة التتبع")
            else:
                logging.debug(f"📋 الفاتورة {invoice[1]} موجودة بالفعل في قاعدة التتبع")

            cursor.close()
            tracking_conn.close()

            return True

        except Exception as e:
            logging.error(f"❌ خطأ في حفظ فاتورة {invoice[1] if len(invoice) > 1 else 'غير محدد'} للتتبع: {e}")
            return False

    def save_expense_to_tracking(self, expense):
        """حفظ مصروف في جدول التتبع على الخادم الاحتياطي فقط - جميع الأعمدة"""
        try:
            tracking_conn = self.get_tracking_connection()
            if not tracking_conn:
                logging.warning("⚠️ لا يمكن حفظ المصروف - الخادم الاحتياطي غير متاح")
                return False

            cursor = tracking_conn.cursor()

            # التحقق من وجود المصروف أولاً
            cursor.execute("SELECT COUNT(*) FROM expense_tracking WHERE aysal_serial = ?", (str(expense[1]),))
            exists = cursor.fetchone()[0] > 0

            if not exists:
                # إدراج مصروف جديد فقط - جميع الأعمدة
                cursor.execute("""
                    INSERT INTO expense_tracking
                    (company_name_doing_aysal, aysal_serial, company_name, the_mony, the_reson,
                     the_date, al_mostalem, is_done, sarf_date, casher_name, tkalyef_main_acc,
                     tkalyef_sup_acc, sent_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, GETDATE())
                """, (
                    expense[0] if len(expense) > 0 else '',  # company_name_doing_aysal
                    expense[1] if len(expense) > 1 else '',  # aysal_serial
                    expense[2] if len(expense) > 2 else '',  # company_name
                    expense[3] if len(expense) > 3 else 0,   # the_mony
                    expense[4] if len(expense) > 4 else '',  # the_reson
                    expense[5] if len(expense) > 5 else None, # the_date
                    expense[6] if len(expense) > 6 else '',  # al_mostalem
                    expense[7] if len(expense) > 7 else 0,   # is_done
                    expense[8] if len(expense) > 8 else None, # sarf_date
                    expense[9] if len(expense) > 9 else '',  # casher_name
                    expense[10] if len(expense) > 10 else '', # tkalyef_main_acc
                    expense[11] if len(expense) > 11 else ''  # tkalyef_sup_acc
                ))

                tracking_conn.commit()
                logging.info(f"✅ تم حفظ مصروف {expense[1]} في قاعدة التتبع")
            else:
                logging.debug(f"📋 المصروف {expense[1]} موجود بالفعل في قاعدة التتبع")

            cursor.close()
            tracking_conn.close()

            return True

        except Exception as e:
            logging.error(f"❌ خطأ في حفظ مصروف {expense[1] if len(expense) > 1 else 'غير محدد'} للتتبع: {e}")
            return False

    def save_box_movement_to_tracking(self, movement):
        """حفظ حركة صندوق في جدول التتبع على الخادم الاحتياطي فقط - جميع الأعمدة"""
        try:
            tracking_conn = self.get_tracking_connection()
            if not tracking_conn:
                logging.warning("⚠️ لا يمكن حفظ حركة الصندوق - الخادم الاحتياطي غير متاح")
                return False

            cursor = tracking_conn.cursor()

            # التحقق من وجود حركة الصندوق أولاً
            cursor.execute("SELECT COUNT(*) FROM box_movements_tracking WHERE box_move_num = ?", (str(movement[8]),))
            exists = cursor.fetchone()[0] > 0

            if not exists:
                # إدراج حركة صندوق جديدة فقط - جميع الأعمدة
                cursor.execute("""
                    INSERT INTO box_movements_tracking
                    (company_name, bank_name, mony_kind, input_mony, output_mony,
                     the_resone, movement_date, the_creadit, box_move_num, sent_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, GETDATE())
                """, (
                    movement[0] if len(movement) > 0 else '',  # company_name
                    movement[1] if len(movement) > 1 else '',  # bank_name
                    movement[2] if len(movement) > 2 else '',  # mony_kind
                    movement[3] if len(movement) > 3 else 0,   # input_mony
                    movement[4] if len(movement) > 4 else 0,   # output_mony
                    movement[5] if len(movement) > 5 else '',  # the_resone
                    movement[6] if len(movement) > 6 else None, # movement_date
                    movement[7] if len(movement) > 7 else 0,   # the_creadit
                    movement[8] if len(movement) > 8 else ''   # box_move_num
                ))

                tracking_conn.commit()
                logging.info(f"✅ تم حفظ حركة صندوق {movement[8]} في قاعدة التتبع")
            else:
                logging.debug(f"📋 حركة الصندوق {movement[8]} موجودة بالفعل في قاعدة التتبع")

            cursor.close()
            tracking_conn.close()

            return True

        except Exception as e:
            logging.error(f"❌ خطأ في حفظ حركة صندوق {movement[8] if len(movement) > 8 else 'غير محدد'} للتتبع: {e}")
            return False

    def is_authorized(self, user_id):
        """فحص صلاحية المستخدم"""
        return user_id in AUTHORIZED_USERS

    def send_single_invoice_details(self, chat_id, invoice_data, invoice_number, total_invoices):
        """إرسال تفاصيل فاتورة واحدة مع جميع الأصناف"""
        try:
            info = invoice_data['info']
            items = invoice_data['items']
            
            invoice_total = sum(item['total_price'] for item in items)
            
            # معلومات الفاتورة
            msg = f"🧾 الفاتورة #{invoice_number}/{total_invoices} - رقم {info['serial']}\n"
            msg += "=" * 35 + "\n"
            msg += f"🏢 الشركة: {info['company']}\n"
            msg += f"📅 التاريخ: {info['date'].strftime('%Y-%m-%d %H:%M') if info['date'] else 'غير محدد'}\n"
            msg += f"👤 المشتري: {info['buyer']}\n"
            msg += f"👨‍💼 المستخدم: {info['user']}\n"
            msg += f"🏪 المخزن: {info['store']}\n"
            
            # حالة الدفع والتسليم
            payment_icon = "✅" if info['payment_status'] == 'تم السداد' else "⏳"
            supply_icon = "✅" if info['supply_status'] == ' تم التسليم' else "⏳"
            
            msg += f"💳 الدفع: {payment_icon} {info['payment_status']}\n"
            msg += f"🚚 التسليم: {supply_icon} {info['supply_status']}\n"
            msg += f"🧾 رقم الإيصال: {info['receipt_number'] if info['receipt_number'] else 'غير محدد'}\n"
            msg += f"💰 إجمالي الفاتورة: {invoice_total:,.0f} ج\n\n"
            
            self.bot.send_message(chat_id, msg)
            
            # تفاصيل جميع الأصناف بدون حدود
            items_msg = f"📦 أصناف الفاتورة ({len(items)} صنف):\n"
            items_msg += "=" * 35 + "\n\n"
            
            for i, item in enumerate(items, 1):
                status_icons = ""
                if item['is_paid'] == 'تم السداد':
                    status_icons += "💳"
                if item['is_supplied'] == ' تم التسليم':
                    status_icons += "🚚"
                if item['is_returned']:
                    status_icons += "↩️"
                
                items_msg += f"{i}. {item['name']}\n"
                items_msg += f"   🔢 كود: {item['code']}\n"
                items_msg += f"   📊 كمية: {item['quantity']:g}\n"
                items_msg += f"   💵 سعر الوحدة: {item['unit_price']:,.0f} ج\n"
                items_msg += f"   💰 الإجمالي: {item['total_price']:,.0f} ج\n"
                items_msg += f"   🏷️ الفئة: {item['category']}\n"
                
                if item['is_returned']:
                    items_msg += f"   ↩️ مرتد: {item['return_quantity']:g} - {item['return_status']}\n"
                
                items_msg += f"   📋 الحالة: {status_icons}\n\n"
                
                # تقسيم الرسالة إذا كانت طويلة (كل 10 أصناف)
                if i % 10 == 0 or i == len(items):
                    self.bot.send_message(chat_id, items_msg)
                    items_msg = ""
                    time.sleep(0.5)
            
            if items_msg:
                self.bot.send_message(chat_id, items_msg)
            
        except Exception as e:
            logging.error(f"خطأ في إرسال تفاصيل الفاتورة: {e}")
            self.bot.send_message(chat_id, f"❌ خطأ في عرض تفاصيل الفاتورة: {e}")

    def send_daily_summary(self, chat_id):
        """إرسال ملخص اليوم"""
        try:
            conn = self.get_primary_connection()
            cursor = conn.cursor()
            
            # إحصائيات الفواتير
            cursor.execute("""
                SELECT 
                    COUNT(DISTINCT fatora_serial) as invoice_count,
                    COUNT(*) as item_count,
                    SUM(good_total_price) as total_sales,
                    COUNT(DISTINCT company_name) as company_count
                FROM buying_fatora
                WHERE CAST(fatora_date AS DATE) = CAST(GETDATE() AS DATE)
            """)
            
            invoice_stats = cursor.fetchone()
            
            # حركات الصندوق
            cursor.execute("""
                SELECT 
                    SUM(ISNULL(input_mony, 0)) as total_input,
                    SUM(ISNULL(output_mony, 0)) as total_output,
                    COUNT(*) as movement_count
                FROM box_movments
                WHERE CAST(movement_date AS DATE) = CAST(GETDATE() AS DATE)
            """)
            
            box_stats = cursor.fetchone()
            conn.close()
            
            # تنسيق التقرير
            msg = f"📊 ملخص أعمال اليوم\n"
            msg += f"📅 {datetime.now().strftime('%Y-%m-%d')}\n"
            msg += "=" * 35 + "\n\n"
            
            msg += "💰 الفواتير:\n"
            msg += f"  🧾 عدد الفواتير: {invoice_stats[0] or 0:,}\n"
            msg += f"  📦 عدد الأصناف: {invoice_stats[1] or 0:,}\n"
            msg += f"  💵 إجمالي المبيعات: {invoice_stats[2] or 0:,.0f} ج\n"
            msg += f"  🏢 عدد الشركات: {invoice_stats[3] or 0}\n\n"
            
            msg += "💳 حركات الصندوق:\n"
            msg += f"  ⬆️ إجمالي الدخل: {box_stats[0] or 0:,.0f} ج\n"
            msg += f"  ⬇️ إجمالي الخرج: {box_stats[1] or 0:,.0f} ج\n"
            msg += f"  📈 الصافي: {(box_stats[0] or 0) - (box_stats[1] or 0):,.0f} ج\n"
            msg += f"  🔄 عدد الحركات: {box_stats[2] or 0:,}\n\n"
            
            msg += f"⏰ آخر تحديث: {datetime.now().strftime('%H:%M:%S')}"
            
            self.bot.send_message(chat_id, msg)
            
        except Exception as e:
            logging.error(f"خطأ في ملخص اليوم: {e}")
            self.bot.send_message(chat_id, f"❌ خطأ في إعداد الملخص: {e}")

    def get_expenses_report(self, chat_id, days):
        """تقرير المصروفات الاحترافي"""
        try:
            conn = self.get_primary_connection()
            cursor = conn.cursor()

            # الحصول على المصروفات
            cursor.execute("""
                SELECT
                    company_name_doing_aysal,
                    aysal_serial,
                    the_mony,
                    the_reson,
                    the_date,
                    company_name
                FROM masrofat
                WHERE the_date >= DATEADD(DAY, -?, GETDATE())
                ORDER BY the_date DESC
            """, (days,))

            expenses = cursor.fetchall()
            conn.close()

            if not expenses:
                self.bot.send_message(chat_id, f"💸 لا توجد مصروفات في آخر {days} يوم")
                return

            # تحليل المصروفات
            total_expenses = sum(float(row[2] or 0) for row in expenses)
            companies_expenses = {}
            categories_expenses = {}

            for expense in expenses:
                company = expense[0] or expense[5] or "غير محدد"
                amount = float(expense[2] or 0)
                reason = expense[3] or "غير محدد"

                # تجميع حسب الشركة
                if company not in companies_expenses:
                    companies_expenses[company] = 0
                companies_expenses[company] += amount

                # تجميع حسب السبب
                if reason not in categories_expenses:
                    categories_expenses[reason] = 0
                categories_expenses[reason] += amount

            # الرسالة الرئيسية
            header = f"💸 تقرير المصروفات الكامل - آخر {days} يوم\n"
            header += "=" * 40 + "\n\n"
            header += f"📊 إجمالي المصروفات: {total_expenses:,.0f} ج\n"
            header += f"📋 عدد المصروفات: {len(expenses):,}\n"
            header += f"🏢 عدد الشركات: {len(companies_expenses)}\n\n"
            header += f"✅ سيتم عرض جميع الـ {len(expenses)} مصروف بالتفصيل الكامل\n"
            header += f"🚫 بدون حدود أو تقييد\n\n"

            self.bot.send_message(chat_id, header)

            # جميع الشركات مصروفاً (بدون حدود)
            all_companies = sorted(companies_expenses.items(), key=lambda x: x[1], reverse=True)
            if all_companies:
                companies_msg = f"🏢 جميع الشركات مصروفاً ({len(all_companies)} شركة):\n"
                companies_msg += "=" * 30 + "\n"
                for i, (company, amount) in enumerate(all_companies, 1):
                    companies_msg += f"{i}. {company}: {amount:,.0f} ج\n"
                companies_msg += "\n"
                self.bot.send_message(chat_id, companies_msg)

            # جميع فئات المصروفات (بدون حدود)
            all_categories = sorted(categories_expenses.items(), key=lambda x: x[1], reverse=True)
            if all_categories:
                categories_msg = f"📂 جميع فئات المصروفات ({len(all_categories)} فئة):\n"
                categories_msg += "=" * 30 + "\n"
                for i, (category, amount) in enumerate(all_categories, 1):
                    categories_msg += f"{i}. {category}: {amount:,.0f} ج\n"
                categories_msg += "\n"
                self.bot.send_message(chat_id, categories_msg)

            # تفاصيل جميع المصروفات (بدون حدود)
            details_msg = f"📋 تفاصيل جميع المصروفات ({len(expenses)} مصروف):\n"
            details_msg += "=" * 35 + "\n\n"

            for i, expense in enumerate(expenses, 1):
                company = expense[0] or expense[5] or "غير محدد"
                serial = int(expense[1]) if expense[1] else 0
                amount = float(expense[2] or 0)
                reason = expense[3] or "غير محدد"
                date = expense[4]

                details_msg += f"{i}. مصروف #{serial}\n"
                details_msg += f"   🏢 الشركة: {company}\n"
                details_msg += f"   💰 المبلغ: {amount:,.0f} ج\n"
                details_msg += f"   📝 السبب: {reason}\n"
                details_msg += f"   📅 التاريخ: {date.strftime('%Y-%m-%d %H:%M') if date else 'غير محدد'}\n\n"

                # إرسال كل 5 مصروفات
                if i % 5 == 0 or i == len(expenses):
                    self.bot.send_message(chat_id, details_msg)
                    details_msg = ""
                    time.sleep(1)

            if details_msg:
                self.bot.send_message(chat_id, details_msg)

        except Exception as e:
            logging.error(f"خطأ في تقرير المصروفات: {e}")
            self.bot.send_message(chat_id, f"❌ خطأ في تقرير المصروفات: {e}")

    def send_unpaid_invoices_report(self, chat_id):
        """تقرير الفواتير غير المدفوعة"""
        try:
            conn = self.get_primary_connection()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT
                    company_name, fatora_serial, fatora_date, the_buyer,
                    SUM(good_total_price) as total_amount,
                    COUNT(*) as items_count,
                    MAX(user_name) as user_name
                FROM buying_fatora
                WHERE is_payed != 'تم السداد' OR is_payed IS NULL
                GROUP BY company_name, fatora_serial, fatora_date, the_buyer
                ORDER BY fatora_date DESC
            """)

            unpaid = cursor.fetchall()
            conn.close()

            if not unpaid:
                self.bot.send_message(chat_id, "✅ جميع الفواتير مدفوعة!")
                return

            total_debt = sum(float(row[4] or 0) for row in unpaid)

            msg = f"📋 الفواتير غير المدفوعة ({len(unpaid)} فاتورة)\n"
            msg += f"💰 إجمالي المديونية: {total_debt:,.0f} ج\n"
            msg += "=" * 40 + "\n\n"

            # إرسال جميع الفواتير غير المدفوعة
            for i, invoice in enumerate(unpaid, 1):
                msg += f"{i}. فاتورة {int(invoice[1])}\n"
                msg += f"   🏢 {invoice[0]}\n"
                msg += f"   👤 {invoice[3]}\n"
                msg += f"   📅 {invoice[2].strftime('%Y-%m-%d') if invoice[2] else 'غير محدد'}\n"
                msg += f"   💰 {invoice[4]:,.0f} ج ({invoice[5]} صنف)\n\n"

                # إرسال كل 10 فواتير
                if i % 10 == 0 or i == len(unpaid):
                    self.bot.send_message(chat_id, msg)
                    msg = ""
                    time.sleep(1)

            if msg:
                self.bot.send_message(chat_id, msg)

        except Exception as e:
            logging.error(f"خطأ في الفواتير غير المدفوعة: {e}")
            self.bot.send_message(chat_id, f"❌ خطأ: {e}")

    def get_box_movements_report(self, chat_id, days):
        """تقرير حركات الصندوق الاحترافي"""
        try:
            conn = self.get_primary_connection()
            cursor = conn.cursor()

            # الحصول على حركات الصندوق
            cursor.execute("""
                SELECT
                    company_name,
                    input_mony,
                    output_mony,
                    movement_date,
                    the_resone,
                    box_move_num
                FROM box_movments
                WHERE movement_date >= DATEADD(DAY, -?, GETDATE())
                ORDER BY movement_date DESC
            """, (days,))

            movements = cursor.fetchall()
            conn.close()

            if not movements:
                self.bot.send_message(chat_id, f"💰 لا توجد حركات صندوق في آخر {days} يوم")
                return

            # تحليل الحركات
            total_input = sum(float(row[1] or 0) for row in movements)
            total_output = sum(float(row[2] or 0) for row in movements)
            net_movement = total_input - total_output

            companies_input = {}
            companies_output = {}

            for movement in movements:
                company = movement[0] or "غير محدد"
                input_amount = float(movement[1] or 0)
                output_amount = float(movement[2] or 0)

                if input_amount > 0:
                    if company not in companies_input:
                        companies_input[company] = 0
                    companies_input[company] += input_amount

                if output_amount > 0:
                    if company not in companies_output:
                        companies_output[company] = 0
                    companies_output[company] += output_amount

            # الرسالة الرئيسية
            header = f"💰 تقرير حركات الصندوق - آخر {days} يوم\n"
            header += "=" * 40 + "\n\n"
            header += f"⬆️ إجمالي الدخل: {total_input:,.0f} ج\n"
            header += f"⬇️ إجمالي الخرج: {total_output:,.0f} ج\n"
            header += f"📈 الصافي: {net_movement:,.0f} ج\n"
            header += f"🔄 عدد الحركات: {len(movements):,}\n\n"

            # تحديد لون الصافي
            if net_movement > 0:
                header += "✅ الصندوق في حالة ربح\n\n"
            elif net_movement < 0:
                header += "⚠️ الصندوق في حالة خسارة\n\n"
            else:
                header += "⚖️ الصندوق متوازن\n\n"

            self.bot.send_message(chat_id, header)

            # جميع الشركات دخلاً (بدون حدود)
            if companies_input:
                all_input = sorted(companies_input.items(), key=lambda x: x[1], reverse=True)
                input_msg = f"⬆️ جميع الشركات دخلاً ({len(all_input)} شركة):\n"
                input_msg += "=" * 25 + "\n"
                for i, (company, amount) in enumerate(all_input, 1):
                    input_msg += f"{i}. {company}: {amount:,.0f} ج\n"
                input_msg += "\n"
                self.bot.send_message(chat_id, input_msg)

            # جميع الشركات خرجاً (بدون حدود)
            if companies_output:
                all_output = sorted(companies_output.items(), key=lambda x: x[1], reverse=True)
                output_msg = f"⬇️ جميع الشركات خرجاً ({len(all_output)} شركة):\n"
                output_msg += "=" * 25 + "\n"
                for i, (company, amount) in enumerate(all_output, 1):
                    output_msg += f"{i}. {company}: {amount:,.0f} ج\n"
                output_msg += "\n"
                self.bot.send_message(chat_id, output_msg)

            # تفاصيل جميع الحركات (بدون حدود)
            details_msg = f"📋 تفاصيل جميع الحركات ({len(movements)} حركة):\n"
            details_msg += "=" * 35 + "\n\n"

            for i, movement in enumerate(movements, 1):
                company = movement[0] or "غير محدد"
                input_amount = float(movement[1] or 0)
                output_amount = float(movement[2] or 0)
                date = movement[3]
                reason = movement[4] or "غير محدد"
                serial = int(movement[5]) if movement[5] else 0

                movement_type = "⬆️ دخل" if input_amount > 0 else "⬇️ خرج"
                amount = input_amount if input_amount > 0 else output_amount

                details_msg += f"{i}. حركة #{serial} - {movement_type}\n"
                details_msg += f"   🏢 الشركة: {company}\n"
                details_msg += f"   💰 المبلغ: {amount:,.0f} ج\n"
                details_msg += f"   📝 السبب: {reason}\n"
                details_msg += f"   📅 التاريخ: {date.strftime('%Y-%m-%d %H:%M') if date else 'غير محدد'}\n\n"

                # إرسال كل 5 حركات
                if i % 5 == 0 or i == len(movements):
                    self.bot.send_message(chat_id, details_msg)
                    details_msg = ""
                    time.sleep(1)

            if details_msg:
                self.bot.send_message(chat_id, details_msg)

        except Exception as e:
            logging.error(f"خطأ في تقرير حركات الصندوق: {e}")
            self.bot.send_message(chat_id, f"❌ خطأ في تقرير حركات الصندوق: {e}")

    def get_companies_performance_report(self, chat_id, days):
        """تقرير أداء الشركات الاحترافي"""
        try:
            conn = self.get_primary_connection()
            cursor = conn.cursor()

            # الحصول على أداء الشركات من الفواتير
            cursor.execute("""
                SELECT
                    company_name,
                    COUNT(DISTINCT fatora_serial) as invoice_count,
                    COUNT(*) as items_count,
                    SUM(good_total_price) as total_sales,
                    SUM(CASE WHEN is_payed = 'تم السداد' THEN good_total_price ELSE 0 END) as paid_amount,
                    SUM(CASE WHEN is_payed != 'تم السداد' OR is_payed IS NULL THEN good_total_price ELSE 0 END) as unpaid_amount,
                    SUM(CASE WHEN is_supplied = ' تم التسليم' THEN good_total_price ELSE 0 END) as delivered_amount,
                    COUNT(DISTINCT the_buyer) as customers_count,
                    COUNT(DISTINCT user_name) as users_count
                FROM buying_fatora
                WHERE fatora_date >= DATEADD(DAY, -?, GETDATE())
                GROUP BY company_name
                ORDER BY total_sales DESC
            """, (days,))

            companies_data = cursor.fetchall()

            # الحصول على حركات الصندوق للشركات
            cursor.execute("""
                SELECT
                    company_name,
                    SUM(ISNULL(input_mony, 0)) as total_input,
                    SUM(ISNULL(output_mony, 0)) as total_output,
                    COUNT(*) as movements_count
                FROM box_movments
                WHERE movement_date >= DATEADD(DAY, -?, GETDATE())
                GROUP BY company_name
            """, (days,))

            box_data = cursor.fetchall()

            # الحصول على المصروفات للشركات
            cursor.execute("""
                SELECT
                    ISNULL(company_name_doing_aysal, company_name) as company,
                    SUM(ISNULL(the_mony, 0)) as total_expenses,
                    COUNT(*) as expenses_count
                FROM masrofat
                WHERE the_date >= DATEADD(DAY, -?, GETDATE())
                GROUP BY ISNULL(company_name_doing_aysal, company_name)
            """, (days,))

            expenses_data = cursor.fetchall()
            conn.close()

            if not companies_data:
                self.bot.send_message(chat_id, f"🏢 لا توجد بيانات شركات في آخر {days} يوم")
                return

            # تجميع البيانات
            companies_performance = {}

            # بيانات الفواتير
            for company_data in companies_data:
                company = company_data[0] or "غير محدد"
                companies_performance[company] = {
                    'invoices': int(company_data[1] or 0),
                    'items': int(company_data[2] or 0),
                    'total_sales': float(company_data[3] or 0),
                    'paid_amount': float(company_data[4] or 0),
                    'unpaid_amount': float(company_data[5] or 0),
                    'delivered_amount': float(company_data[6] or 0),
                    'customers': int(company_data[7] or 0),
                    'users': int(company_data[8] or 0),
                    'box_input': 0,
                    'box_output': 0,
                    'box_movements': 0,
                    'expenses': 0,
                    'expenses_count': 0
                }

            # بيانات الصندوق
            for box_item in box_data:
                company = box_item[0] or "غير محدد"
                if company in companies_performance:
                    companies_performance[company]['box_input'] = float(box_item[1] or 0)
                    companies_performance[company]['box_output'] = float(box_item[2] or 0)
                    companies_performance[company]['box_movements'] = int(box_item[3] or 0)

            # بيانات المصروفات
            for expense_item in expenses_data:
                company = expense_item[0] or "غير محدد"
                if company in companies_performance:
                    companies_performance[company]['expenses'] = float(expense_item[1] or 0)
                    companies_performance[company]['expenses_count'] = int(expense_item[2] or 0)

            # الرسالة الرئيسية
            total_companies = len(companies_performance)
            total_sales = sum(comp['total_sales'] for comp in companies_performance.values())
            total_paid = sum(comp['paid_amount'] for comp in companies_performance.values())
            total_unpaid = sum(comp['unpaid_amount'] for comp in companies_performance.values())

            header = f"🏢 تقرير أداء الشركات - آخر {days} يوم\n"
            header += "=" * 40 + "\n\n"
            header += f"🏢 عدد الشركات: {total_companies}\n"
            header += f"💰 إجمالي المبيعات: {total_sales:,.0f} ج\n"
            header += f"✅ المدفوع: {total_paid:,.0f} ج ({(total_paid/total_sales*100) if total_sales > 0 else 0:.1f}%)\n"
            header += f"⏳ المعلق: {total_unpaid:,.0f} ج ({(total_unpaid/total_sales*100) if total_sales > 0 else 0:.1f}%)\n\n"

            self.bot.send_message(chat_id, header)

            # تفاصيل كل شركة
            sorted_companies = sorted(companies_performance.items(), key=lambda x: x[1]['total_sales'], reverse=True)

            for i, (company, data) in enumerate(sorted_companies, 1):
                payment_rate = (data['paid_amount'] / data['total_sales'] * 100) if data['total_sales'] > 0 else 0
                delivery_rate = (data['delivered_amount'] / data['total_sales'] * 100) if data['total_sales'] > 0 else 0
                net_cash_flow = data['box_input'] - data['box_output'] - data['expenses']

                company_msg = f"🏢 {i}. {company}\n"
                company_msg += "=" * 30 + "\n"
                company_msg += f"📊 المبيعات:\n"
                company_msg += f"  • إجمالي المبيعات: {data['total_sales']:,.0f} ج\n"
                company_msg += f"  • عدد الفواتير: {data['invoices']:,}\n"
                company_msg += f"  • عدد الأصناف: {data['items']:,}\n"
                company_msg += f"  • عدد العملاء: {data['customers']:,}\n\n"

                company_msg += f"💳 الدفع:\n"
                company_msg += f"  • مدفوع: {data['paid_amount']:,.0f} ج ({payment_rate:.1f}%)\n"
                company_msg += f"  • معلق: {data['unpaid_amount']:,.0f} ج\n\n"

                company_msg += f"🚚 التسليم:\n"
                company_msg += f"  • مسلم: {data['delivered_amount']:,.0f} ج ({delivery_rate:.1f}%)\n\n"

                company_msg += f"💰 الصندوق:\n"
                company_msg += f"  • دخل: {data['box_input']:,.0f} ج\n"
                company_msg += f"  • خرج: {data['box_output']:,.0f} ج\n"
                company_msg += f"  • عدد الحركات: {data['box_movements']:,}\n\n"

                company_msg += f"💸 المصروفات:\n"
                company_msg += f"  • إجمالي المصروفات: {data['expenses']:,.0f} ج\n"
                company_msg += f"  • عدد المصروفات: {data['expenses_count']:,}\n\n"

                company_msg += f"📈 صافي التدفق النقدي: {net_cash_flow:,.0f} ج\n"

                # تحديد حالة الأداء
                if net_cash_flow > 0 and payment_rate > 80:
                    company_msg += "✅ أداء ممتاز\n\n"
                elif net_cash_flow > 0 and payment_rate > 60:
                    company_msg += "🟡 أداء جيد\n\n"
                elif payment_rate > 50:
                    company_msg += "🟠 أداء متوسط\n\n"
                else:
                    company_msg += "🔴 يحتاج تحسين\n\n"

                self.bot.send_message(chat_id, company_msg)
                time.sleep(1)

        except Exception as e:
            logging.error(f"خطأ في تقرير أداء الشركات: {e}")
            self.bot.send_message(chat_id, f"❌ خطأ في تقرير أداء الشركات: {e}")

    def get_companies_performance_by_date(self, chat_id, target_date):
        """تقرير أداء الشركات بتاريخ محدد"""
        try:
            conn = self.get_primary_connection()
            cursor = conn.cursor()

            # الحصول على أداء الشركات لليوم المحدد
            cursor.execute("""
                SELECT
                    company_name,
                    COUNT(DISTINCT fatora_serial) as invoice_count,
                    COUNT(*) as items_count,
                    SUM(good_total_price) as total_sales,
                    SUM(CASE WHEN is_payed = 'تم السداد' THEN good_total_price ELSE 0 END) as paid_amount,
                    SUM(CASE WHEN is_payed != 'تم السداد' OR is_payed IS NULL THEN good_total_price ELSE 0 END) as unpaid_amount,
                    COUNT(DISTINCT the_buyer) as customers_count
                FROM buying_fatora
                WHERE CAST(fatora_date AS DATE) = CAST(? AS DATE)
                GROUP BY company_name
                ORDER BY total_sales DESC
            """, (target_date,))

            companies_data = cursor.fetchall()
            conn.close()

            if not companies_data:
                self.bot.send_message(chat_id, f"🏢 لا توجد بيانات شركات في يوم {target_date.strftime('%d-%m-%Y')}")
                return

            # الرسالة الرئيسية
            total_companies = len(companies_data)
            total_sales = sum(float(comp[3] or 0) for comp in companies_data)
            total_paid = sum(float(comp[4] or 0) for comp in companies_data)

            header = f"🏢 أداء الشركات يوم {target_date.strftime('%d-%m-%Y')}\n"
            header += "=" * 40 + "\n\n"
            header += f"🏢 عدد الشركات: {total_companies}\n"
            header += f"💰 إجمالي المبيعات: {total_sales:,.0f} ج\n"
            header += f"✅ المدفوع: {total_paid:,.0f} ج\n\n"

            self.bot.send_message(chat_id, header)

            # تفاصيل كل شركة
            for i, company_data in enumerate(companies_data, 1):
                company = company_data[0] or "غير محدد"
                invoices = int(company_data[1] or 0)
                items = int(company_data[2] or 0)
                sales = float(company_data[3] or 0)
                paid = float(company_data[4] or 0)
                unpaid = float(company_data[5] or 0)
                customers = int(company_data[6] or 0)

                payment_rate = (paid / sales * 100) if sales > 0 else 0

                company_msg = f"🏢 {i}. {company}\n"
                company_msg += "=" * 25 + "\n"
                company_msg += f"💰 المبيعات: {sales:,.0f} ج\n"
                company_msg += f"📋 الفواتير: {invoices:,}\n"
                company_msg += f"📦 الأصناف: {items:,}\n"
                company_msg += f"👥 العملاء: {customers:,}\n"
                company_msg += f"✅ مدفوع: {paid:,.0f} ج ({payment_rate:.1f}%)\n"
                company_msg += f"⏳ معلق: {unpaid:,.0f} ج\n\n"

                self.bot.send_message(chat_id, company_msg)
                time.sleep(1)

        except Exception as e:
            logging.error(f"خطأ في أداء الشركات بالتاريخ: {e}")
            self.bot.send_message(chat_id, f"❌ خطأ في تقرير أداء الشركات: {e}")

    def get_box_movements_by_date(self, chat_id, target_date):
        """تقرير حركات الصندوق بتاريخ محدد"""
        try:
            conn = self.get_primary_connection()
            cursor = conn.cursor()

            # الحصول على حركات الصندوق لليوم المحدد
            cursor.execute("""
                SELECT
                    company_name,
                    input_mony,
                    output_mony,
                    movement_date,
                    the_resone,
                    box_move_num
                FROM box_movments
                WHERE CAST(movement_date AS DATE) = CAST(? AS DATE)
                ORDER BY movement_date DESC
            """, (target_date,))

            movements = cursor.fetchall()
            conn.close()

            if not movements:
                self.bot.send_message(chat_id, f"💰 لا توجد حركات صندوق في يوم {target_date.strftime('%d-%m-%Y')}")
                return

            # تحليل الحركات
            total_input = sum(float(row[1] or 0) for row in movements)
            total_output = sum(float(row[2] or 0) for row in movements)
            net_movement = total_input - total_output

            # الرسالة الرئيسية
            header = f"💰 حركات الصندوق يوم {target_date.strftime('%d-%m-%Y')}\n"
            header += "=" * 40 + "\n\n"
            header += f"⬆️ إجمالي الدخل: {total_input:,.0f} ج\n"
            header += f"⬇️ إجمالي الخرج: {total_output:,.0f} ج\n"
            header += f"📈 الصافي: {net_movement:,.0f} ج\n"
            header += f"🔄 عدد الحركات: {len(movements):,}\n\n"

            self.bot.send_message(chat_id, header)

            # تفاصيل جميع الحركات
            details_msg = f"📋 تفاصيل جميع الحركات ({len(movements)} حركة):\n"
            details_msg += "=" * 35 + "\n\n"

            for i, movement in enumerate(movements, 1):
                company = movement[0] or "غير محدد"
                input_amount = float(movement[1] or 0)
                output_amount = float(movement[2] or 0)
                date = movement[3]
                reason = movement[4] or "غير محدد"
                serial = int(movement[5]) if movement[5] else 0

                movement_type = "⬆️ دخل" if input_amount > 0 else "⬇️ خرج"
                amount = input_amount if input_amount > 0 else output_amount

                details_msg += f"{i}. حركة #{serial} - {movement_type}\n"
                details_msg += f"   🏢 الشركة: {company}\n"
                details_msg += f"   💰 المبلغ: {amount:,.0f} ج\n"
                details_msg += f"   📝 السبب: {reason}\n"
                details_msg += f"   📅 الوقت: {date.strftime('%H:%M:%S') if date else 'غير محدد'}\n\n"

                # إرسال كل 5 حركات
                if i % 5 == 0 or i == len(movements):
                    self.bot.send_message(chat_id, details_msg)
                    details_msg = ""
                    time.sleep(1)

            if details_msg:
                self.bot.send_message(chat_id, details_msg)

        except Exception as e:
            logging.error(f"خطأ في حركات الصندوق بالتاريخ: {e}")
            self.bot.send_message(chat_id, f"❌ خطأ في تقرير حركات الصندوق: {e}")

    def get_companies_report(self, chat_id):
        """تقرير الشركات والفروع"""
        try:
            conn = self.get_primary_connection()
            cursor = conn.cursor()

            # إحصائيات الشركات
            cursor.execute("""
                SELECT
                    company_name,
                    COUNT(DISTINCT fatora_serial) as invoices_count,
                    SUM(good_total_price) as total_sales,
                    COUNT(DISTINCT the_store) as stores_count,
                    COUNT(DISTINCT the_buyer) as customers_count
                FROM buying_fatora
                WHERE fatora_date >= DATEADD(month, -1, GETDATE())
                GROUP BY company_name
                ORDER BY total_sales DESC
            """)

            companies = cursor.fetchall()
            cursor.close()
            conn.close()

            if not companies:
                self.bot.send_message(chat_id, "📊 لا توجد بيانات شركات متاحة")
                return

            msg = "🏢 تقرير الشركات والفروع (آخر شهر)\n"
            msg += "=" * 40 + "\n\n"

            total_sales = 0
            total_invoices = 0

            for company in companies:
                name = company[0] or "غير محدد"
                invoices = company[1] or 0
                sales = float(company[2] or 0)
                stores = company[3] or 0
                customers = company[4] or 0

                total_sales += sales
                total_invoices += invoices

                msg += f"🏢 {name}\n"
                msg += f"📄 الفواتير: {invoices:,}\n"
                msg += f"💰 المبيعات: {sales:,.0f} ج\n"
                msg += f"🏪 الفروع: {stores}\n"
                msg += f"👥 العملاء: {customers}\n"
                msg += "-" * 25 + "\n"

            msg += f"\n📊 الإجمالي:\n"
            msg += f"💰 إجمالي المبيعات: {total_sales:,.0f} ج\n"
            msg += f"📄 إجمالي الفواتير: {total_invoices:,}\n"

            self.bot.send_message(chat_id, msg)

        except Exception as e:
            logging.error(f"خطأ في تقرير الشركات: {e}")
            self.bot.send_message(chat_id, f"❌ خطأ في تقرير الشركات: {e}")

    def get_detailed_expenses_report(self, chat_id):
        """تقرير المصروفات التفصيلي"""
        try:
            conn = self.get_primary_connection()
            cursor = conn.cursor()

            # المصروفات حسب الفئة
            cursor.execute("""
                SELECT
                    tkalyef_main_acc,
                    COUNT(*) as count,
                    SUM(the_mony) as total_amount,
                    AVG(the_mony) as avg_amount
                FROM masrofat
                WHERE the_date >= DATEADD(week, -1, GETDATE())
                GROUP BY tkalyef_main_acc
                ORDER BY total_amount DESC
            """)

            categories = cursor.fetchall()

            # أكبر المصروفات
            cursor.execute("""
                SELECT TOP 10
                    the_reson,
                    the_mony,
                    the_date,
                    company_name,
                    casher_name
                FROM masrofat
                WHERE the_date >= DATEADD(week, -1, GETDATE())
                ORDER BY the_mony DESC
            """)

            top_expenses = cursor.fetchall()
            cursor.close()
            conn.close()

            msg = "💰 تقرير المصروفات التفصيلي (آخر أسبوع)\n"
            msg += "=" * 45 + "\n\n"

            if categories:
                msg += "📊 المصروفات حسب الفئة:\n"
                total_expenses = 0

                for category in categories:
                    cat_name = category[0] or "غير محدد"
                    count = category[1] or 0
                    amount = float(category[2] or 0)
                    avg = float(category[3] or 0)

                    total_expenses += amount

                    msg += f"📂 {cat_name}\n"
                    msg += f"   العدد: {count:,} | المبلغ: {amount:,.0f} ج\n"
                    msg += f"   المتوسط: {avg:,.0f} ج\n\n"

                msg += f"💰 إجمالي المصروفات: {total_expenses:,.0f} ج\n\n"

            if top_expenses:
                msg += "🔝 أكبر المصروفات:\n"
                for i, expense in enumerate(top_expenses[:5], 1):
                    reason = expense[0] or "غير محدد"
                    amount = float(expense[1] or 0)
                    date = expense[2]
                    company = expense[3] or "غير محدد"

                    msg += f"{i}. {reason[:30]}...\n"
                    msg += f"   💰 {amount:,.0f} ج - {company}\n"
                    if date:
                        msg += f"   📅 {date.strftime('%Y-%m-%d')}\n"
                    msg += "\n"

            self.bot.send_message(chat_id, msg)

        except Exception as e:
            logging.error(f"خطأ في تقرير المصروفات التفصيلي: {e}")
            self.bot.send_message(chat_id, f"❌ خطأ في تقرير المصروفات: {e}")

    def get_cash_movements_report(self, chat_id):
        """تقرير حركات الصندوق والبنوك"""
        try:
            conn = self.get_primary_connection()
            cursor = conn.cursor()

            # حركات الصندوق الأخيرة
            cursor.execute("""
                SELECT TOP 20
                    company_name,
                    bank_name,
                    mony_kind,
                    input_mony,
                    output_mony,
                    the_resone,
                    movement_date,
                    the_creadit
                FROM box_movments
                WHERE movement_date >= DATEADD(day, -7, GETDATE())
                ORDER BY movement_date DESC
            """)

            movements = cursor.fetchall()
            cursor.close()
            conn.close()

            if not movements:
                self.bot.send_message(chat_id, "🏦 لا توجد حركات صندوق حديثة")
                return

            msg = "🏦 تقرير حركات الصندوق (آخر أسبوع)\n"
            msg += "=" * 40 + "\n\n"

            total_input = 0
            total_output = 0

            for movement in movements:
                company = movement[0] or "غير محدد"
                bank = movement[1] or "غير محدد"
                money_type = movement[2] or "غير محدد"
                input_money = float(movement[3] or 0)
                output_money = float(movement[4] or 0)
                reason = movement[5] or "غير محدد"
                date = movement[6]
                credit = float(movement[7] or 0)

                total_input += input_money
                total_output += output_money

                if input_money > 0:
                    msg += f"📈 إيداع: {input_money:,.0f} ج\n"
                else:
                    msg += f"📉 سحب: {output_money:,.0f} ج\n"

                msg += f"🏢 {company} - {bank}\n"
                msg += f"📝 {reason[:40]}...\n"
                if date:
                    msg += f"📅 {date.strftime('%Y-%m-%d %H:%M')}\n"
                msg += f"💳 الرصيد: {credit:,.0f} ج\n"
                msg += "-" * 25 + "\n"

            msg += f"\n📊 الملخص:\n"
            msg += f"📈 إجمالي الإيداعات: {total_input:,.0f} ج\n"
            msg += f"📉 إجمالي السحوبات: {total_output:,.0f} ج\n"
            msg += f"💰 صافي الحركة: {(total_input - total_output):,.0f} ج\n"

            self.bot.send_message(chat_id, msg)

        except Exception as e:
            logging.error(f"خطأ في تقرير حركات الصندوق: {e}")
            self.bot.send_message(chat_id, f"❌ خطأ في تقرير حركات الصندوق: {e}")

    def get_performance_report(self, chat_id):
        """تقرير الأداء الشامل"""
        try:
            conn = self.get_primary_connection()
            cursor = conn.cursor()

            # إحصائيات شاملة
            cursor.execute("""
                SELECT
                    COUNT(DISTINCT fatora_serial) as total_invoices,
                    SUM(good_total_price) as total_sales,
                    COUNT(DISTINCT company_name) as companies_count,
                    COUNT(DISTINCT the_buyer) as customers_count,
                    AVG(good_total_price) as avg_invoice_value
                FROM buying_fatora
                WHERE fatora_date >= DATEADD(month, -1, GETDATE())
            """)

            stats = cursor.fetchone()

            # أداء الشركات
            cursor.execute("""
                SELECT TOP 5
                    company_name,
                    COUNT(DISTINCT fatora_serial) as invoices,
                    SUM(good_total_price) as sales
                FROM buying_fatora
                WHERE fatora_date >= DATEADD(month, -1, GETDATE())
                GROUP BY company_name
                ORDER BY sales DESC
            """)

            top_companies = cursor.fetchall()

            # المصروفات
            cursor.execute("""
                SELECT
                    COUNT(*) as expenses_count,
                    SUM(the_mony) as total_expenses
                FROM masrofat
                WHERE the_date >= DATEADD(month, -1, GETDATE())
            """)

            expenses_stats = cursor.fetchone()
            cursor.close()
            conn.close()

            msg = "📊 تقرير الأداء الشامل (آخر شهر)\n"
            msg += "=" * 45 + "\n\n"

            if stats:
                total_invoices = stats[0] or 0
                total_sales = float(stats[1] or 0)
                companies_count = stats[2] or 0
                customers_count = stats[3] or 0
                avg_invoice = float(stats[4] or 0)

                msg += "📈 إحصائيات المبيعات:\n"
                msg += f"📄 إجمالي الفواتير: {total_invoices:,}\n"
                msg += f"💰 إجمالي المبيعات: {total_sales:,.0f} ج\n"
                msg += f"🏢 عدد الشركات: {companies_count}\n"
                msg += f"👥 عدد العملاء: {customers_count}\n"
                msg += f"📊 متوسط الفاتورة: {avg_invoice:,.0f} ج\n\n"

            if expenses_stats:
                expenses_count = expenses_stats[0] or 0
                total_expenses = float(expenses_stats[1] or 0)

                msg += "💸 إحصائيات المصروفات:\n"
                msg += f"📝 عدد المصروفات: {expenses_count:,}\n"
                msg += f"💰 إجمالي المصروفات: {total_expenses:,.0f} ج\n\n"

                if stats:
                    net_profit = total_sales - total_expenses
                    profit_margin = (net_profit / total_sales * 100) if total_sales > 0 else 0

                    msg += "💹 الأداء المالي:\n"
                    msg += f"💰 صافي الربح: {net_profit:,.0f} ج\n"
                    msg += f"📊 هامش الربح: {profit_margin:.1f}%\n\n"

            if top_companies:
                msg += "🏆 أفضل الشركات أداءً:\n"
                for i, company in enumerate(top_companies, 1):
                    name = company[0] or "غير محدد"
                    invoices = company[1] or 0
                    sales = float(company[2] or 0)

                    msg += f"{i}. {name}\n"
                    msg += f"   📄 {invoices:,} فاتورة | 💰 {sales:,.0f} ج\n"

            self.bot.send_message(chat_id, msg)

        except Exception as e:
            logging.error(f"خطأ في تقرير الأداء: {e}")
            self.bot.send_message(chat_id, f"❌ خطأ في تقرير الأداء: {e}")

    def get_invoices_search(self, chat_id):
        """البحث في الفواتير مع أزرار"""
        try:
            markup = types.InlineKeyboardMarkup(row_width=2)

            # أزرار البحث
            btn_by_serial = types.InlineKeyboardButton("🔢 بحث برقم الفاتورة", callback_data="search_serial")
            btn_by_customer = types.InlineKeyboardButton("👤 بحث بالعميل", callback_data="search_customer")
            btn_by_company = types.InlineKeyboardButton("🏢 بحث بالشركة", callback_data="search_company")
            btn_by_date = types.InlineKeyboardButton("📅 بحث بالتاريخ", callback_data="search_date")
            btn_by_amount = types.InlineKeyboardButton("💰 بحث بالمبلغ", callback_data="search_amount")
            btn_unpaid = types.InlineKeyboardButton("⏳ الفواتير المعلقة", callback_data="search_unpaid")

            markup.add(btn_by_serial, btn_by_customer)
            markup.add(btn_by_company, btn_by_date)
            markup.add(btn_by_amount, btn_unpaid)

            msg = "🔍 البحث في الفواتير\n\n"
            msg += "اختر نوع البحث المطلوب:"

            self.bot.send_message(chat_id, msg, reply_markup=markup)

        except Exception as e:
            logging.error(f"خطأ في البحث في الفواتير: {e}")
            self.bot.send_message(chat_id, f"❌ خطأ في البحث: {e}")

    def get_quick_stats(self, chat_id):
        """إحصائيات سريعة"""
        try:
            conn = self.get_primary_connection()
            cursor = conn.cursor()

            # إحصائيات اليوم
            cursor.execute("""
                SELECT
                    COUNT(DISTINCT fatora_serial) as today_invoices,
                    SUM(good_total_price) as today_sales
                FROM buying_fatora
                WHERE CAST(fatora_date AS DATE) = CAST(GETDATE() AS DATE)
            """)

            today_stats = cursor.fetchone()

            # إحصائيات الأسبوع
            cursor.execute("""
                SELECT
                    COUNT(DISTINCT fatora_serial) as week_invoices,
                    SUM(good_total_price) as week_sales
                FROM buying_fatora
                WHERE fatora_date >= DATEADD(week, -1, GETDATE())
            """)

            week_stats = cursor.fetchone()

            # مصروفات اليوم
            cursor.execute("""
                SELECT
                    COUNT(*) as today_expenses,
                    SUM(the_mony) as today_expenses_amount
                FROM masrofat
                WHERE CAST(the_date AS DATE) = CAST(GETDATE() AS DATE)
            """)

            expenses_today = cursor.fetchone()
            cursor.close()
            conn.close()

            msg = "⚡ إحصائيات سريعة\n"
            msg += "=" * 25 + "\n\n"

            if today_stats:
                today_invoices = today_stats[0] or 0
                today_sales = float(today_stats[1] or 0)

                msg += "📅 اليوم:\n"
                msg += f"📄 الفواتير: {today_invoices:,}\n"
                msg += f"💰 المبيعات: {today_sales:,.0f} ج\n\n"

            if week_stats:
                week_invoices = week_stats[0] or 0
                week_sales = float(week_stats[1] or 0)

                msg += "📊 هذا الأسبوع:\n"
                msg += f"📄 الفواتير: {week_invoices:,}\n"
                msg += f"💰 المبيعات: {week_sales:,.0f} ج\n\n"

            if expenses_today:
                today_exp_count = expenses_today[0] or 0
                today_exp_amount = float(expenses_today[1] or 0)

                msg += "💸 مصروفات اليوم:\n"
                msg += f"📝 العدد: {today_exp_count:,}\n"
                msg += f"💰 المبلغ: {today_exp_amount:,.0f} ج\n\n"

                if today_stats:
                    net_today = today_sales - today_exp_amount
                    msg += f"💹 صافي اليوم: {net_today:,.0f} ج\n"

            self.bot.send_message(chat_id, msg)

        except Exception as e:
            logging.error(f"خطأ في الإحصائيات السريعة: {e}")
            self.bot.send_message(chat_id, f"❌ خطأ في الإحصائيات: {e}")

    def check_new_invoices(self):
        """فحص الفواتير الجديدة - الصف الكامل من 4-9 فما بعد"""
        try:
            # الحصول على البيانات من القاعدة الأساسية (للقراءة فقط)
            conn = self.get_primary_connection()
            if not conn:
                return []

            cursor = conn.cursor()

            # جلب الفواتير الجديدة فقط بعد آخر فحص
            cursor.execute("""
                SELECT
                    bf.company_name,
                    bf.fatora_serial,
                    bf.fatora_date,
                    bf.user_name,
                    bf.the_buyer,
                    bf.the_store,
                    bf.good_name,
                    bf.good_code,
                    bf.good_quentity,
                    bf.unit_price,
                    bf.good_total_price,
                    bf.good_kind,
                    bf.is_payed,
                    bf.is_supplied,
                    bf.aysal_serial,
                    bf.is_mortadat,
                    bf.store_mortadat_quen,
                    bf.mortadat_status,
                    bf.supplied_date,
                    bf.total_selling_price,
                    bf.total_rb7,
                    bf.good_category,
                    bf.mortg3at_serial,
                    bf.al_lesta_price,
                    bf.gomla_discount_percentage,
                    bf.printing_gomla_price,
                    bf.gomla_price_is_updated,
                    bf.gomla_old_price
                FROM buying_fatora bf
                WHERE bf.fatora_date >= ?
                ORDER BY bf.fatora_date DESC, bf.fatora_serial DESC
            """, (self.last_invoice_check,))

            all_invoices = cursor.fetchall()
            cursor.close()
            conn.close()

            if not all_invoices:
                return []

            # استخدام ذاكرة محلية لتتبع الفواتير المرسلة (بدلاً من قاعدة البيانات)
            if not hasattr(self, 'sent_invoices'):
                self.sent_invoices = set()

            # فلترة الفواتير الجديدة فقط مع حد أقصى
            new_invoices = []
            max_notifications = 10  # حد أقصى 10 إشعارات في المرة الواحدة

            for invoice in all_invoices:
                if len(new_invoices) >= max_notifications:
                    break

                invoice_key = f"{invoice[1]}_{invoice[0]}_{invoice[7]}"  # fatora_serial_company_good_code
                if invoice_key not in self.sent_invoices:
                    new_invoices.append(invoice)
                    self.sent_invoices.add(invoice_key)  # إضافة للذاكرة المحلية

            if len(all_invoices) > max_notifications:
                logging.info(f"تم العثور على {len(new_invoices)} فاتورة جديدة (محدود بـ {max_notifications}) من أصل {len(all_invoices)}")
            else:
                logging.info(f"تم العثور على {len(new_invoices)} فاتورة جديدة من أصل {len(all_invoices)}")
            return new_invoices

        except Exception as e:
            logging.error(f"خطأ في فحص الفواتير الجديدة: {e}")
            return []

    def check_new_expenses(self):
        """فحص المصروفات الجديدة - الصف الكامل من 4-9 فما بعد"""
        try:
            # الحصول على البيانات من القاعدة الأساسية (للقراءة فقط)
            conn = self.get_primary_connection()
            if not conn:
                return []

            cursor = conn.cursor()

            # جلب جميع المصروفات الكاملة من 4-9 فما بعد
            cursor.execute("""
                SELECT
                    m.company_name_doing_aysal,
                    m.aysal_serial,
                    m.company_name,
                    m.the_mony,
                    m.the_reson,
                    m.the_date,
                    m.al_mostalem,
                    m.is_done,
                    m.sarf_date,
                    m.casher_name,
                    m.tkalyef_main_acc,
                    m.tkalyef_sup_acc
                FROM masrofat m
                WHERE m.the_date >= '2024-04-09 00:00:00'
                ORDER BY m.the_date DESC, m.aysal_serial DESC
            """)

            all_expenses = cursor.fetchall()
            cursor.close()
            conn.close()

            if not all_expenses:
                return []

            # استخدام ذاكرة محلية لتتبع المصروفات المرسلة (بدلاً من قاعدة البيانات)
            if not hasattr(self, 'sent_expenses'):
                self.sent_expenses = set()

            # فلترة المصروفات الجديدة فقط
            new_expenses = []
            for expense in all_expenses:
                expense_key = f"{expense[0]}_{expense[1]}"  # company_doing_aysal_serial
                if expense_key not in self.sent_expenses:
                    new_expenses.append(expense)
                    self.sent_expenses.add(expense_key)  # إضافة للذاكرة المحلية

            logging.info(f"تم العثور على {len(new_expenses)} مصروف جديد من أصل {len(all_expenses)}")
            return new_expenses

        except Exception as e:
            logging.error(f"خطأ في فحص المصروفات الجديدة: {e}")
            return []

    def check_new_box_movements(self):
        """فحص حركات الصندوق الجديدة - الصف الكامل من 4-9 فما بعد"""
        try:
            # الحصول على البيانات من القاعدة الأساسية (للقراءة فقط)
            conn = self.get_primary_connection()
            if not conn:
                return []

            cursor = conn.cursor()

            # جلب جميع حركات الصندوق الكاملة من 4-9 فما بعد
            cursor.execute("""
                SELECT
                    bm.company_name,
                    bm.bank_name,
                    bm.mony_kind,
                    bm.input_mony,
                    bm.output_mony,
                    bm.the_resone,
                    bm.movement_date,
                    bm.the_creadit,
                    bm.box_move_num
                FROM box_movments bm
                WHERE bm.movement_date >= '2024-04-09 00:00:00'
                ORDER BY bm.movement_date DESC, bm.box_move_num DESC
            """)

            all_movements = cursor.fetchall()
            cursor.close()
            conn.close()

            if not all_movements:
                return []

            # استخدام ذاكرة محلية لتتبع حركات الصندوق المرسلة (بدلاً من قاعدة البيانات)
            if not hasattr(self, 'sent_movements'):
                self.sent_movements = set()

            # فلترة حركات الصندوق الجديدة فقط
            new_movements = []
            for movement in all_movements:
                movement_key = f"{movement[0]}_{movement[8]}"  # company_name_box_move_num
                if movement_key not in self.sent_movements:
                    new_movements.append(movement)
                    self.sent_movements.add(movement_key)  # إضافة للذاكرة المحلية

            logging.info(f"تم العثور على {len(new_movements)} حركة صندوق جديدة من أصل {len(all_movements)}")
            return new_movements

        except Exception as e:
            logging.error(f"خطأ في فحص حركات الصندوق الجديدة: {e}")
            return []

    def check_payment_updates(self):
        """فحص تحديثات الدفع للفواتير"""
        try:
            conn = self.get_primary_connection()
            cursor = conn.cursor()

            # البحث عن الفواتير التي تم تحديث حالة الدفع لها مؤخراً
            cursor.execute("""
                SELECT DISTINCT
                    fatora_serial, company_name, the_buyer,
                    SUM(good_total_price) as total_amount,
                    MAX(is_payed) as payment_status,
                    MAX(is_supplied) as supply_status,
                    MAX(fatora_date) as invoice_date
                FROM buying_fatora
                WHERE fatora_date >= DATEADD(HOUR, -2, GETDATE())
                  AND is_payed = 'تم السداد'
                GROUP BY fatora_serial, company_name, the_buyer
                ORDER BY MAX(fatora_date) DESC
            """)

            paid_invoices = cursor.fetchall()

            # البحث عن الفواتير التي تم تسليمها مؤخراً
            cursor.execute("""
                SELECT DISTINCT
                    fatora_serial, company_name, the_buyer,
                    SUM(good_total_price) as total_amount,
                    MAX(is_payed) as payment_status,
                    MAX(is_supplied) as supply_status,
                    MAX(fatora_date) as invoice_date
                FROM buying_fatora
                WHERE fatora_date >= DATEADD(HOUR, -2, GETDATE())
                  AND is_supplied = ' تم التسليم'
                GROUP BY fatora_serial, company_name, the_buyer
                ORDER BY MAX(fatora_date) DESC
            """)

            delivered_invoices = cursor.fetchall()
            conn.close()

            return paid_invoices, delivered_invoices

        except Exception as e:
            logging.error(f"خطأ في فحص تحديثات الدفع: {e}")
            return [], []

    def check_large_box_movements(self):
        """فحص حركات الصندوق الكبيرة"""
        try:
            conn = self.get_primary_connection()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT
                    company_name,
                    input_mony,
                    output_mony,
                    movement_date,
                    the_resone,
                    box_move_num
                FROM box_movments
                WHERE movement_date > ? AND movement_date <= GETDATE()
                  AND (ISNULL(input_mony, 0) > 1000 OR ISNULL(output_mony, 0) > 1000)
                ORDER BY movement_date DESC
            """, (self.last_box_check,))

            large_movements = cursor.fetchall()
            conn.close()

            return large_movements

        except Exception as e:
            logging.error(f"خطأ في فحص حركات الصندوق الكبيرة: {e}")
            return []

    def send_notification(self, user_id, message):
        """إرسال إشعار لمستخدم مع معالجة أفضل للأخطاء"""
        try:
            self.bot.send_message(user_id, message)
            return True
        except Exception as e:
            error_msg = str(e)
            if "chat not found" in error_msg.lower():
                logging.warning(f"المستخدم {user_id} لم يبدأ محادثة مع البوت بعد")
            elif "conflict" in error_msg.lower():
                logging.warning(f"تضارب في البوت - يوجد بوت آخر يعمل")
            else:
                logging.error(f"خطأ في إرسال إشعار للمستخدم {user_id}: {e}")
            return False

    def format_invoice_notification(self, invoice):
        """تنسيق إشعار الفاتورة الجديدة"""
        try:
            company = str(invoice[0]) if invoice[0] else "غير محدد"  # الشركة أولاً
            serial = str(invoice[1]) if invoice[1] else "غير محدد"   # رقم الفاتورة ثانياً
            date = invoice[2]
            user = str(invoice[3]) if invoice[3] else "غير محدد"
            buyer = str(invoice[4]) if invoice[4] else "غير محدد"
            store = str(invoice[5]) if invoice[5] else "غير محدد"

            # البحث عن المبلغ في الأعمدة المختلفة
            amount = 0
            for i in range(6, min(len(invoice), 15)):  # البحث في الأعمدة المحتملة للمبلغ
                try:
                    if invoice[i] and str(invoice[i]).replace('.', '').replace(',', '').isdigit():
                        amount = float(invoice[i])
                        break
                except:
                    continue

            payment_status = str(invoice[12]) if len(invoice) > 12 and invoice[12] else "غير محدد"
            supply_status = str(invoice[13]) if len(invoice) > 13 and invoice[13] else "غير محدد"

            payment_icon = "✅" if payment_status == 'تم السداد' else "⏳"
            supply_icon = "✅" if supply_status == 'تم التسليم' else "⏳"

            msg = f"🆕 فاتورة جديدة!\n\n"
            msg += f"🧾 رقم الفاتورة: {serial}\n"
            msg += f"🏢 الشركة: {company}\n"
            msg += f"👤 المشتري: {buyer}\n"
            msg += f"🏪 المخزن: {store}\n"
            msg += f"👨‍💼 المستخدم: {user}\n"
            msg += f"📅 التاريخ: {date.strftime('%Y-%m-%d %H:%M') if date else 'غير محدد'}\n"
            msg += f"💰 المبلغ: {amount:,.0f} ج\n"
            msg += f"💳 الدفع: {payment_icon} {payment_status}\n"
            msg += f"🚚 التسليم: {supply_icon} {supply_status}\n"

            return msg

        except Exception as e:
            logging.error(f"خطأ في تنسيق إشعار الفاتورة: {e}")
            return f"🆕 فاتورة جديدة رقم {invoice[0]} - {invoice[1] if len(invoice) > 1 else 'غير محدد'}"

    def format_expense_notification(self, expense):
        """تنسيق إشعار المصروف الجديد"""
        try:
            serial = int(expense[0]) if expense[0] else 0
            company_doing = expense[1] or "غير محدد"
            company = expense[2] or company_doing
            amount = float(expense[3] or 0)
            reason = expense[4] or "غير محدد"
            date = expense[5]

            msg = f"💸 مصروف جديد!\n\n"
            msg += f"🧾 رقم المصروف: {serial}\n"
            msg += f"🏢 الشركة: {company}\n"
            msg += f"💰 المبلغ: {amount:,.0f} ج\n"
            msg += f"📝 السبب: {reason}\n"
            msg += f"📅 التاريخ: {date.strftime('%Y-%m-%d %H:%M') if date else 'غير محدد'}\n"

            return msg

        except Exception as e:
            logging.error(f"خطأ في تنسيق إشعار المصروف: {e}")
            return f"💸 مصروف جديد - {expense[3]:,.0f} ج"

    def format_box_movement_notification(self, movement):
        """تنسيق إشعار حركة الصندوق الجديدة"""
        try:
            company = str(movement[0]) if movement[0] else "غير محدد"
            bank_name = str(movement[1]) if movement[1] else "غير محدد"
            money_type = str(movement[2]) if movement[2] else "غير محدد"
            input_money = float(movement[3]) if movement[3] else 0
            output_money = float(movement[4]) if movement[4] else 0
            reason = str(movement[5]) if movement[5] else "غير محدد"
            date = movement[6]
            credit = float(movement[7]) if movement[7] else 0
            move_num = str(movement[8]) if movement[8] else "غير محدد"

            # تحديد نوع الحركة والمبلغ
            if input_money > 0:
                movement_type = "إيداع"
                amount = input_money
                icon = "📈"
            else:
                movement_type = "سحب"
                amount = output_money
                icon = "📉"

            msg = f"🏦 حركة صندوق جديدة!\n\n"
            msg += f"🏢 الشركة: {company}\n"
            msg += f"🏦 البنك: {bank_name}\n"
            msg += f"💱 نوع العملة: {money_type}\n"
            msg += f"{icon} نوع الحركة: {movement_type}\n"
            msg += f"💰 المبلغ: {amount:,.0f} ج\n"
            msg += f"📝 السبب: {reason}\n"
            msg += f"📅 التاريخ: {date.strftime('%Y-%m-%d %H:%M') if date else 'غير محدد'}\n"
            msg += f"💳 الرصيد: {credit:,.0f} ج\n"
            msg += f"🔢 رقم الحركة: {move_num}\n"

            return msg

        except Exception as e:
            logging.error(f"خطأ في تنسيق إشعار حركة الصندوق: {e}")
            return f"🏦 حركة صندوق جديدة - {movement[8] if len(movement) > 8 else 'غير محدد'}"

    def format_payment_notification(self, invoice):
        """تنسيق إشعار الدفع"""
        try:
            serial = int(invoice[0])
            company = invoice[1]
            buyer = invoice[2]
            amount = float(invoice[3] or 0)

            msg = f"💳 تم سداد فاتورة!\n\n"
            msg += f"🧾 رقم الفاتورة: {serial}\n"
            msg += f"🏢 الشركة: {company}\n"
            msg += f"👤 المشتري: {buyer}\n"
            msg += f"💰 المبلغ المسدد: {amount:,.0f} ج\n"
            msg += f"✅ تم السداد بالكامل\n"

            return msg

        except Exception as e:
            logging.error(f"خطأ في تنسيق إشعار الدفع: {e}")
            return f"� تم سداد فاتورة {invoice[0]}"

    def format_delivery_notification(self, invoice):
        """تنسيق إشعار التسليم"""
        try:
            serial = int(invoice[0])
            company = invoice[1]
            buyer = invoice[2]
            amount = float(invoice[3] or 0)

            msg = f"🚚 تم تسليم فاتورة!\n\n"
            msg += f"🧾 رقم الفاتورة: {serial}\n"
            msg += f"🏢 الشركة: {company}\n"
            msg += f"👤 المشتري: {buyer}\n"
            msg += f"💰 قيمة الفاتورة: {amount:,.0f} ج\n"
            msg += f"✅ تم التسليم بالكامل\n"

            return msg

        except Exception as e:
            logging.error(f"خطأ في تنسيق إشعار التسليم: {e}")
            return f"🚚 تم تسليم فاتورة {invoice[0]}"

    def format_box_movement_notification(self, movement):
        """تنسيق إشعار حركة الصندوق الكبيرة"""
        try:
            company = movement[0] or "غير محدد"
            input_amount = float(movement[1] or 0)
            output_amount = float(movement[2] or 0)
            date = movement[3]
            reason = movement[4] or "غير محدد"
            serial = int(movement[5]) if movement[5] else 0

            if input_amount > 0:
                movement_type = "⬆️ دخل كبير"
                amount = input_amount
                icon = "💰"
            else:
                movement_type = "⬇️ خرج كبير"
                amount = output_amount
                icon = "💸"

            msg = f"{icon} حركة صندوق كبيرة!\n\n"
            msg += f"🔄 نوع الحركة: {movement_type}\n"
            msg += f"🏢 الشركة: {company}\n"
            msg += f"💰 المبلغ: {amount:,.0f} ج\n"
            msg += f"📝 السبب: {reason}\n"
            msg += f"📅 التاريخ: {date.strftime('%Y-%m-%d %H:%M') if date else 'غير محدد'}\n"
            msg += f"🧾 رقم الحركة: {serial}\n"

            return msg

        except Exception as e:
            logging.error(f"خطأ في تنسيق إشعار حركة الصندوق: {e}")
            return f"💰 حركة صندوق كبيرة - {movement[1] or movement[2]:,.0f} ج"

    def process_notifications(self):
        """معالجة الإشعارات الشاملة"""
        try:
            subscribers = self.get_notification_subscribers()
            if not subscribers:
                logging.warning("لا يوجد مشتركين في الإشعارات")
                return

            notifications_sent = 0

            # 1. فحص الفواتير الجديدة - إرسال مباشر بدون كتابة جداول
            new_invoices = self.check_new_invoices()
            if new_invoices:
                for invoice in new_invoices:
                    notification_msg = self.format_invoice_notification(invoice)
                    for subscriber in subscribers:
                        success = self.send_notification(subscriber, notification_msg)
                        if success:
                            notifications_sent += 1
                            logging.info(f"تم إرسال إشعار فاتورة جديدة {invoice[1]} للمستخدم {subscriber}")
                        time.sleep(0.5)

                logging.info(f"تم معالجة {len(new_invoices)} فاتورة جديدة")

            # 2. فحص المصروفات الجديدة - إرسال مباشر بدون كتابة جداول
            new_expenses = self.check_new_expenses()
            if new_expenses:
                for expense in new_expenses:
                    notification_msg = self.format_expense_notification(expense)
                    for subscriber in subscribers:
                        success = self.send_notification(subscriber, notification_msg)
                        if success:
                            notifications_sent += 1
                            logging.info(f"تم إرسال إشعار مصروف جديد {expense[1]} للمستخدم {subscriber}")
                        time.sleep(0.5)

                logging.info(f"تم معالجة {len(new_expenses)} مصروف جديد")

            # 3. فحص حركات الصندوق الجديدة - إرسال مباشر للحركات الكبيرة فقط
            new_movements = self.check_new_box_movements()
            if new_movements:
                for movement in new_movements:
                    # إرسال إشعار للحركات الكبيرة فقط (أكثر من 1000 جنيه)
                    amount = float(movement[3]) if movement[3] else float(movement[4]) if movement[4] else 0
                    if amount >= 1000:
                        notification_msg = self.format_box_movement_notification(movement)
                        for subscriber in subscribers:
                            success = self.send_notification(subscriber, notification_msg)
                            if success:
                                notifications_sent += 1
                                logging.info(f"تم إرسال إشعار حركة صندوق {movement[8]} للمستخدم {subscriber}")
                            time.sleep(0.5)

                logging.info(f"تم معالجة {len(new_movements)} حركة صندوق جديدة")

                self.last_expense_check = datetime.now()
                logging.info(f"تم معالجة {len(new_expenses)} مصروف جديد")

            # 3. فحص تحديثات الدفع والتسليم
            paid_invoices, delivered_invoices = self.check_payment_updates()

            # إشعارات الدفع
            if paid_invoices:
                for invoice in paid_invoices:
                    notification_msg = self.format_payment_notification(invoice)

                    for subscriber in subscribers:
                        success = self.send_notification(subscriber, notification_msg)
                        if success:
                            notifications_sent += 1
                            logging.info(f"تم إرسال إشعار دفع فاتورة {invoice[0]} للمستخدم {subscriber}")
                        time.sleep(0.5)

                logging.info(f"تم معالجة {len(paid_invoices)} تحديث دفع")

            # إشعارات التسليم
            if delivered_invoices:
                for invoice in delivered_invoices:
                    notification_msg = self.format_delivery_notification(invoice)

                    for subscriber in subscribers:
                        success = self.send_notification(subscriber, notification_msg)
                        if success:
                            notifications_sent += 1
                            logging.info(f"تم إرسال إشعار تسليم فاتورة {invoice[0]} للمستخدم {subscriber}")
                        time.sleep(0.5)

                logging.info(f"تم معالجة {len(delivered_invoices)} تحديث تسليم")

            # 4. فحص حركات الصندوق الكبيرة
            large_movements = self.check_large_box_movements()
            if large_movements:
                for movement in large_movements:
                    notification_msg = self.format_box_movement_notification(movement)

                    for subscriber in subscribers:
                        success = self.send_notification(subscriber, notification_msg)
                        if success:
                            notifications_sent += 1
                            logging.info(f"تم إرسال إشعار حركة صندوق كبيرة للمستخدم {subscriber}")
                        time.sleep(0.5)

                self.last_box_check = datetime.now()
                logging.info(f"تم معالجة {len(large_movements)} حركة صندوق كبيرة")

            if notifications_sent == 0:
                logging.info("لا توجد تحديثات جديدة")
            else:
                logging.info(f"تم إرسال {notifications_sent} إشعار إجمالي")

        except Exception as e:
            logging.error(f"خطأ في معالجة الإشعارات: {e}")

    def notification_loop(self):
        """حلقة الإشعارات"""
        logging.info("🔔 بدء نظام الإشعارات...")
        
        while self.is_running:
            try:
                current_time = datetime.now()
                self.process_notifications()
                self.last_check = current_time
                time.sleep(self.check_interval)
                
            except Exception as e:
                logging.error(f"خطأ في حلقة الإشعارات: {e}")
                time.sleep(30)

    def start_system(self):
        """بدء النظام الموحد"""
        try:
            logging.info("🚀 بدء النظام الموحد...")

            # بدء نظام الإشعارات في thread منفصل
            self.is_running = True
            self.notification_thread = threading.Thread(target=self.notification_loop)
            self.notification_thread.daemon = True
            self.notification_thread.start()

            logging.info("✅ تم بدء نظام الإشعارات")

            # بدء البوت مع معالجة تضارب التوكن
            logging.info("🤖 بدء البوت...")

            # محاولة إيقاف أي بوت آخر يستخدم نفس التوكن
            try:
                self.bot.stop_polling()
                time.sleep(2)
            except:
                pass

            # بدء البوت مع معالجة الأخطاء
            retry_count = 0
            max_retries = 3

            while retry_count < max_retries and self.is_running:
                try:
                    self.bot.polling(none_stop=True, interval=1, timeout=20)
                    break
                except Exception as polling_error:
                    retry_count += 1
                    if "409" in str(polling_error) or "Conflict" in str(polling_error):
                        logging.warning(f"⚠️ تضارب في التوكن، محاولة {retry_count}/{max_retries}")
                        time.sleep(5)
                        try:
                            self.bot.stop_polling()
                            time.sleep(3)
                        except:
                            pass
                    else:
                        logging.error(f"❌ خطأ في البوت: {polling_error}")
                        break

            if retry_count >= max_retries:
                logging.error("❌ فشل في بدء البوت بعد عدة محاولات")

        except Exception as e:
            logging.error(f"❌ خطأ في النظام: {e}")
            self.is_running = False

    def stop_system(self):
        """إيقاف النظام"""
        logging.info("⏹️ إيقاف النظام...")
        self.is_running = False
        
        if self.notification_thread:
            self.notification_thread.join(timeout=10)
        
        logging.info("✅ تم إيقاف النظام")

    def run(self):
        """تشغيل البوت مع نظام الإشعارات"""
        try:
            logging.info("🚀 بدء تشغيل البوت الموحد...")
            print("🤖 البوت جاهز للاستخدام!")
            print("📱 يمكنك الآن إرسال /start للبوت")
            print("🔔 نظام الإشعارات يعمل كل 30 ثانية")
            print("=" * 50)

            # بدء نظام الإشعارات
            self.start_system()

        except KeyboardInterrupt:
            print("\n🛑 تم إيقاف البوت بواسطة المستخدم")
            logging.info("تم إيقاف البوت بواسطة المستخدم")
            self.stop_system()
        except Exception as e:
            print(f"❌ خطأ في تشغيل البوت: {e}")
            logging.error(f"خطأ في تشغيل البوت: {e}")
            self.stop_system()
            raise e

def main():
    """الدالة الرئيسية"""
    print("🤖 النظام الموحد - البوت + الإشعارات")
    print("=" * 50)
    
    system = UnifiedBotSystem()
    
    try:
        system.start_system()
    except KeyboardInterrupt:
        print("\n⏹️ تم طلب إيقاف النظام...")
    except Exception as e:
        logging.error(f"❌ خطأ: {e}")
    finally:
        system.stop_system()
        print("👋 تم إيقاف النظام")

if __name__ == "__main__":
    main()
